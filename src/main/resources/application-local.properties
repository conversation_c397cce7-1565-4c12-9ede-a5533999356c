# Spring Boot application properties for LOCAL environment
server.port=8080
server.servlet.context-path=/internal
spring.main.allow-bean-definition-overriding=true

# logging
logging.config=classpath:logback-spring.xml
spring.main.banner-mode=off

# server
server.max-http-request-header-size=2MB
server.servlet.encoding.charset=UTF-8
server.servlet.session.tracking-modes=url

# PostgreSQL connection configuration
spring.datasource.url=${POSTGRESQL_URL:******************************************************************************************************************************}
spring.datasource.username=${POSTGRESQL_USER:vinsf_user}
spring.datasource.password=${POSTGRESQL_PASSWORD:vinsf_pass}
spring.jpa.properties.hibernate.default_schema=${DB_DEFAULT_SCHEMA:miniapp_db}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.minimumIdle=2
spring.datasource.hikari.maximumPoolSize=4
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=CoreJPAHikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.generate-ddl=true
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=false

# REDDISON
redisson.config.address=${REDISSON_CONFIG_ADDRESS:redis://localhost:6379}
redisson.config.password=${REDISSON_CONFIG_PASSWORD:}
redisson.config.database=${REDISSON_CONFIG_DATABASE:0}
redisson.config.response.timeout=${REDISSON_CONFIG_RESPONSE_TIMEOUT:3000}
redisson.config.connection.timeout=${REDISSON_CONFIG_CONNECTION_TIMEOUT:3000}
redisson.config.connection.idle.time=${REDISSON_CONFIG_CONNECTION_IDLE_TIME:300000}
redisson.config.connection.max=${REDISSON_CONFIG_CONNECTION_MAX:64}
redisson.config.connection.min=${REDISSON_CONFIG_CONNECTION_MIN:24}

# KVROCKS
kvrocks.config.address=${KVROCKS_CONFIG_ADDRESS:redis://localhost:6666}
kvrocks.config.password=${KVROCKS_CONFIG_PASSWORD:}
kvrocks.config.database=${KVROCKS_CONFIG_DATABASE:0}
kvrocks.config.response.timeout=${KVROCKS_CONFIG_RESPONSE_TIMEOUT:3000}
kvrocks.config.connection.timeout=${KVROCKS_CONFIG_CONNECTION_TIMEOUT:3000}
kvrocks.config.connection.idle.time=${KVROCKS_CONFIG_CONNECTION_IDLE_TIME:300000}
kvrocks.config.connection.max=${KVROCKS_CONFIG_CONNECTION_MAX:64}
kvrocks.config.connection.min=${KVROCKS_CONFIG_CONNECTION_MIN:24}

# KAFKA
kafka.properties.sasl.mechanism=${KAFKA_SASL_MECHANISM:PLAIN}
kafka.properties.bootstrap.servers=${KAFKA_BOOTSTRAP_SERVER:localhost:9092}
kafka.properties.sasl.jaas.config=${KAFKA_SASL_JAAS_CONFIG:org.apache.kafka.common.security.plain.PlainLoginModule required username="vinsf-user" password="vinsf-pass";}
kafka.properties.security.protocol=${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
kafka.properties.basic.auth.credentials.source=USER_INFO
kafka.properties.schema.registry.basic.auth.user.info=${KAFKA_REGISTRY_USERNAME:}:${KAFKA_REGISTRY_PASSWORD:}
kafka.properties.schema.registry.url=${KAFKA_REGISTRY_URL:}
kafka.properties.group.id=core-svc-group-${spring.profiles.active}
kafka.properties.auto.offset.reset=earliest
kafka.producer.properties.linger.ms=1

# Redis pub/sub channel
redis.channel.change-event=${REDIS_CHANNEL_CHANGE_EVENT:CHANGE_EVENT}

# AWS
aws.region=${AWS_REGION:ap-southeast-1}
aws.localstack.endpoint=http://localhost:4566
aws.localstack.accessKey=test
aws.localstack.secretKey=test

## KMS
aws.kms.key-id=${KMS_KEY_ID:vfs-miniapp-encryption-key}

## S3
aws.s3.bucket.name=${S3_BUCKET_NAME:vfs-miniapp-bucket}
aws.s3.forcePathStyle=true