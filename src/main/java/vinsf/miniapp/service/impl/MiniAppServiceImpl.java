package vinsf.miniapp.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vinsf.miniapp.dto.MiniAppDto;
import vinsf.miniapp.dto.MiniAppFilter;
import vinsf.miniapp.exception.ResourceExistedException;
import vinsf.miniapp.exception.ResourceNotFoundException;
import vinsf.miniapp.mapper.MiniAppMapper;
import vinsf.miniapp.model.MiniApp;
import vinsf.miniapp.publisher.RedisPublisher;
import vinsf.miniapp.repository.MiniAppRepository;
import vinsf.miniapp.service.MiniAppService;
import vinsf.miniapp.specification.MiniAppSpecification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Service implementation for MiniApp entity operations
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniAppServiceImpl implements MiniAppService {
    private static final int CACHE_TTL_MINUTES = 15;
    private static final int CACHE_MAX_SIZE = 100;

    private final MiniAppRepository miniAppRepository;
    private final MiniAppMapper miniAppMapper;
    private final RedisPublisher redisPublisher;

    // Caffeine cache for id -> dto
    private Cache<String, MiniAppDto> idCache;
    // Caffeine cache for code -> id mapping
    private Cache<String, String> codeToIdCache;

    @PostConstruct
    public void init() {
        // Initialize Caffeine caches
        idCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        codeToIdCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        log.info("MiniAppService initialized with Caffeine cache");
    }

    @Override
    public MiniAppDto create(@Valid MiniAppDto dto) {
        log.info("Creating MiniApp with code: {}", dto.code());

        // Validate code uniqueness
        if (miniAppRepository.existsByCode(dto.code())) {
            log.warn("MiniApp code already exists: {}", dto.code());
            throw new ResourceExistedException("MiniApp", "code", dto.code());
        }

        MiniApp entity = miniAppMapper.toEntity(dto);
        MiniApp saved = saveAndPublishChangeEvent(entity);

        log.info("MiniApp created successfully with id: {}", saved.getId());

        return miniAppMapper.toDto(saved);
    }

    @Override
    public MiniAppDto getById(String id) {
        return optById(id)
                .orElseThrow(() -> {
                    log.warn("MiniApp not found with id: {}", id);
                    return new ResourceNotFoundException("MiniApp", "id", id);
                });
    }

    @Override
    public MiniAppDto getByCode(String code) {
        return optByCode(code)
                .orElseThrow(() -> {
                    log.warn("MiniApp not found with code: {}", code);
                    return new ResourceNotFoundException("MiniApp", "code", code);
                });
    }

    @Override
    public Optional<MiniAppDto> optById(String id) {
        log.debug("Getting MiniApp by id: {}", id);

        // Try to get from cache first
        MiniAppDto cached = idCache.getIfPresent(id);
        if (cached != null) {
            log.debug("MiniApp found in cache for id: {}", id);
            return Optional.of(cached);
        }

        // Load from database
        Optional<MiniAppDto> miniApp = miniAppRepository.findById(id).map(miniAppMapper::toDto);

        miniApp.ifPresent(this::putToCache);

        return miniApp;
    }

    @Override
    public Optional<MiniAppDto> optByCode(String code) {
        log.debug("Getting MiniApp by code: {}", code);

        // Try to get id from code cache
        String cachedId = codeToIdCache.getIfPresent(code);
        if (cachedId != null) {
            log.debug("Found id in code cache for code: {}", code);
            return optById(cachedId);
        }

        // Load from database
        Optional<MiniAppDto> miniApp = miniAppRepository.findByCode(code).map(miniAppMapper::toDto);

        miniApp.ifPresent(this::putToCache);

        return miniApp;
    }

    @Override
    public MiniAppDto update(String id, @Valid MiniAppDto dto) {
        log.info("Updating MiniApp with id: {}", id);

        MiniApp existing = miniAppRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniApp not found with id: {}", id);
                    return new ResourceNotFoundException("MiniApp", "id", id);
                });

        // Update entity
        miniAppMapper.update(existing, dto);

        MiniApp saved = saveAndPublishChangeEvent(existing);

        log.info("MiniApp updated successfully with id: {}", id);

        return miniAppMapper.toDto(saved);
    }

    @Override
    public MiniAppDto updatePartial(String id, MiniAppDto dto) {
        log.info("Partial updating MiniApp with id: {}", id);

        MiniApp existing = miniAppRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniApp not found with id: {}", id);
                    return new ResourceNotFoundException("MiniApp", "id", id);
                });

        // Apply partial updates (only non-null fields)
        miniAppMapper.partialUpdate(existing, dto);

        MiniApp saved = saveAndPublishChangeEvent(existing);

        log.info("MiniApp partially updated successfully with id: {}", id);

        return miniAppMapper.toDto(saved);
    }

    @Override
    public void delete(String id) {
        log.info("Deleting MiniApp with id: {}", id);

        MiniApp existing = miniAppRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("MiniApp not found with id: {}", id);
                    return new ResourceNotFoundException("MiniApp", "id", id);
                });

        existing.setActive(false);
        saveAndPublishChangeEvent(existing);

        log.info("MiniApp deleted successfully with id: {}", id);
    }

    @Override
    public Page<MiniAppDto> search(MiniAppFilter filter, Pageable pageable) {
        log.debug("Searching MiniApps with filter: {}", filter);

        Specification<MiniApp> spec = MiniAppSpecification.fromFilter(filter);
        Page<MiniApp> page = miniAppRepository.findAll(spec, pageable);

        return page.map(miniAppMapper::toDto);
    }

    @Override
    public boolean existsByCode(String code) {
        return miniAppRepository.existsByCode(code);
    }

    @Override
    public void invalidateCache(Set<String> ids, Set<String> codes) {
        log.info("Invalidating cache for ids: {} and codes: {}", ids, codes);
        idCache.invalidateAll(ids);
        codeToIdCache.invalidateAll(codes);
    }

    /**
     * Adds the given MiniApp to the caches by storing its ID and code mappings.
     *
     * @param miniApp the MiniApp instance to be added to the caches
     */
    private void putToCache(MiniAppDto miniApp) {
        idCache.put(miniApp.id(), miniApp);
        codeToIdCache.put(miniApp.code(), miniApp.id());
    }

    /**
     * Save entity and publish entity change event
     *
     * @param entity the MiniApp entity
     * @return saved entity
     */
    private MiniApp saveAndPublishChangeEvent(MiniApp entity) {
        MiniApp saved = miniAppRepository.save(entity);
        publishCacheInvalidation(saved.getId(), saved.getCode());
        return saved;
    }

    /**
     * Publish cache invalidation event to Redis Pub/Sub
     *
     * @param id   the MiniApp ID
     * @param code the MiniApp code
     */
    private void publishCacheInvalidation(String id, String code) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublisher.sendChangeEvent(MiniApp.ENTITY_NAME, id, code);
                }
            });
        } else {
            redisPublisher.sendChangeEvent(MiniApp.ENTITY_NAME, id, code);
        }
    }
}

