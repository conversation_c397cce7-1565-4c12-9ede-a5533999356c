package vinsf.miniapp.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vinsf.miniapp.constant.AppErrorCode;
import vinsf.miniapp.dto.EncryptedSecret;
import vinsf.miniapp.dto.ServiceResponse;
import vinsf.miniapp.exception.BusinessLogicException;
import vinsf.miniapp.service.KmsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.kms.model.*;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Implementation of KmsService using AWS KMS
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AwsKmsServiceImpl implements KmsService {
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 128;
    private static final String KEY_SPEC_ALGORITHM = "AES";

    private final KmsClient kmsClient;

    @Value("${aws.kms.key-id:vfs-miniapp-encryption-key}")
    private String kmsKeyId;

    @Override
    public EncryptedSecret encrypt(String plaintext) {
        try {
            log.debug("Encrypting data using AWS KMS key: {}", kmsKeyId);

            // Generate data key from KMS
            GenerateDataKeyRequest dataKeyRequest = GenerateDataKeyRequest.builder()
                    .keyId(kmsKeyId)
                    .keySpec(DataKeySpec.AES_256)
                    .build();

            GenerateDataKeyResponse dataKeyResponse = kmsClient.generateDataKey(dataKeyRequest);

            // Extract plaintext and encrypted data key
            byte[] plaintextDataKey = dataKeyResponse.plaintext().asByteArray();
            byte[] encryptedDataKey = dataKeyResponse.ciphertextBlob().asByteArray();

            // Generate random IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            SecureRandom secureRandom = new SecureRandom();
            secureRandom.nextBytes(iv);

            // Encrypt data using AES-GCM
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(plaintextDataKey, KEY_SPEC_ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmSpec);

            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // Get last 4 characters of plaintext for verification
            String last4 = plaintext.length() >= 4
                    ? plaintext.substring(plaintext.length() - 4)
                    : plaintext;

            // Encode to Base64
            String ciphertextB64 = Base64.getEncoder().encodeToString(ciphertext);
            String ivB64 = Base64.getEncoder().encodeToString(iv);
            String encryptedDataKeyB64 = Base64.getEncoder().encodeToString(encryptedDataKey);

            log.debug("Data encrypted successfully");

            return new EncryptedSecret(
                    ciphertextB64,
                    ivB64,
                    encryptedDataKeyB64,
                    kmsKeyId,
                    last4
            );

        } catch (Exception e) {
            log.error("Failed to encrypt data using AWS KMS", e);
            throw new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.MINIAPP_KMS_ENCRYPTION_FAILED)
            );
        }
    }

    @Override
    public String decrypt(EncryptedSecret encryptedSecret) {
        try {
            log.debug("Decrypting data using AWS KMS");

            // Decode from Base64
            byte[] ciphertext = Base64.getDecoder().decode(encryptedSecret.ciphertextB64());
            byte[] iv = Base64.getDecoder().decode(encryptedSecret.ivB64());
            byte[] encryptedDataKey = Base64.getDecoder().decode(encryptedSecret.encryptedDataKeyB64());

            // Decrypt data key using KMS
            DecryptRequest decryptRequest = DecryptRequest.builder()
                    .ciphertextBlob(SdkBytes.fromByteArray(encryptedDataKey))
                    .keyId(encryptedSecret.keyId())
                    .build();

            DecryptResponse decryptResponse = kmsClient.decrypt(decryptRequest);
            byte[] plaintextDataKey = decryptResponse.plaintext().asByteArray();

            // Decrypt data using AES-GCM
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(plaintextDataKey, KEY_SPEC_ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmSpec);

            byte[] plaintext = cipher.doFinal(ciphertext);

            log.debug("Data decrypted successfully");

            return new String(plaintext, StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.error("Failed to decrypt data using AWS KMS", e);
            throw new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.MINIAPP_KMS_DECRYPTION_FAILED)
            );
        }
    }

}
