package vinsf.miniapp.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.Set;

/**
 * DTO for creating/updating MiniApp webhooks
 * Does not include secret field as it is auto-generated
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public record MiniAppWebhookCreateDto(
        String id,

        @NotBlank(message = "Webhook URL is required")
        String url,

        Set<String> events,

        @NotNull(message = "Active status is required")
        Boolean active
) {
}

