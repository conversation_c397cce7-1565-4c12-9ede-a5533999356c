package vinsf.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReleaseStatus {
    ACTIVE("ACTIVE"),
    INACTIVE("PAUSED"),
    ARCHIVED("ARCHIVED");

    private final String code;

    @JsonCreator
    public static ReleaseStatus fromCode(String value) {
        for (ReleaseStatus status : ReleaseStatus.values()) {
            if (status.code.equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid ReleaseStatus value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
