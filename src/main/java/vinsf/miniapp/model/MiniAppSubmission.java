package vinsf.miniapp.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vinsf.miniapp.constant.SubmissionType;
import vinsf.miniapp.model.base.BaseEntityUUID;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_submissions")
public class MiniAppSubmission extends BaseEntityUUID {
    @Column(name = "miniapp_id", nullable = false)
    private String miniappId;

    @Column(name = "bundle_id", nullable = false)
    private Long bundleId;

    private SubmissionType type;
}
