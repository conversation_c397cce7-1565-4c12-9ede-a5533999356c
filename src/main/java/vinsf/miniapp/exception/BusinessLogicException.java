package vinsf.miniapp.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import vinsf.miniapp.constant.AppErrorCode;
import vinsf.miniapp.dto.ServiceResponse;

import java.io.Serial;

@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessLogicException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;
    private ServiceResponse<?> payload;

    public BusinessLogicException(ServiceResponse<?> payload) {
        this.payload = payload;
    }

    public BusinessLogicException(AppErrorCode errorCode, Object... args) {
        this(ServiceResponse.error(errorCode, args));
    }
}

