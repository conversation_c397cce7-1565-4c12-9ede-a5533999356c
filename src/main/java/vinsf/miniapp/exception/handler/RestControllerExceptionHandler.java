package vinsf.miniapp.exception.handler;

import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import vinsf.miniapp.constant.AppErrorCode;
import vinsf.miniapp.dto.ServiceResponse;
import vinsf.miniapp.exception.*;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Global exception handler for REST controllers.
 * Handles all exceptions and converts them to consistent ServiceResponse format.
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@ControllerAdvice
@Slf4j
public class RestControllerExceptionHandler extends ResponseEntityExceptionHandler {

    /**
     * Handle BusinessLogicException - thrown for business rule violations.
     * Returns BAD_REQUEST status with error details from the exception payload.
     */
    @ExceptionHandler(BusinessLogicException.class)
    public ResponseEntity<ServiceResponse<?>> handleBusinessLogicException(BusinessLogicException ex) {
        ServiceResponse<?> response = ex.getPayload();
        if (response != null) {
            log.warn("Business logic exception: code={}, message={}", response.getCode(), response.getMessage());
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        } else {
            log.error("Business logic exception with null payload", ex);
            return handleAllExceptions(ex);
        }
    }

    /**
     * Handle ResourceNotFoundException - thrown when a requested resource is not found.
     * Returns RESOURCE_NOT_FOUND status with resource details.
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ServiceResponse<?>> handleResourceNotFoundException(ResourceNotFoundException ex) {
        log.warn("Resource not found: {}", ex.getMessage());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.RESOURCE_NOT_FOUND.getCode())
                .message(AppErrorCode.NOT_FOUND.getMessage())
                .messages(List.of(ex.getMessage()))
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(ResourceExistedException.class)
    public ResponseEntity<ServiceResponse<?>> handleResourceExistedException(ResourceExistedException ex) {
        log.warn("Resource already exists: {}", ex.getMessage());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Resource already exists")
                .messages(List.of(ex.getMessage()))
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(InvalidStatusException.class)
    public ResponseEntity<ServiceResponse<?>> handleInvalidStatusException(InvalidStatusException ex) {
        log.warn("Invalid status: {}", ex.getMessage());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.INVALID_STATUS.getCode())
                .message("Invalid status")
                .messages(List.of(ex.getMessage()))
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle IgnoreProcessingException - thrown when processing should be ignored.
     * Returns BAD_REQUEST status with error details from the exception payload.
     */
    @ExceptionHandler(IgnoreProcessingException.class)
    public ResponseEntity<ServiceResponse<?>> handleIgnoreProcessingException(IgnoreProcessingException ex) {
        ServiceResponse<?> response = ex.getPayload();
        log.warn("Ignore processing exception: code={}, message={}",
                response != null ? response.getCode() : "N/A",
                response != null ? response.getMessage() : "N/A");
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle MethodArgumentNotValidException - thrown when @Valid validation fails on request body.
     * Returns BAD_REQUEST status with detailed validation error messages.
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        List<String> errors = new ArrayList<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.add(String.format("%s: %s", error.getField(), error.getDefaultMessage()));
        }
        for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
            errors.add(String.format("%s: %s", error.getObjectName(), error.getDefaultMessage()));
        }

        log.warn("Validation failed: {}", errors);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Validation failed")
                .messages(errors)
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle ConstraintViolationException - thrown when @Validated validation fails on method parameters.
     * Returns BAD_REQUEST status with detailed constraint violation messages.
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ServiceResponse<?>> handleConstraintViolationException(ConstraintViolationException ex) {
        List<String> errors = ex.getConstraintViolations().stream()
                .map(violation -> String.format("%s: %s",
                        violation.getPropertyPath(),
                        violation.getMessage()))
                .collect(Collectors.toList());

        log.warn("Constraint violation: {}", errors);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Constraint violation")
                .messages(errors)
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle HttpMessageNotReadableException - thrown when request body cannot be read or parsed.
     * Returns BAD_REQUEST status indicating invalid request body format.
     */
    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        log.warn("Message not readable: {}", ex.getMessage());
        String errorMessage = ex.getMessage().split(":")[0];


        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Message not readable")
                .messages(List.of(errorMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle NoResourceFoundException - thrown when Spring cannot find a resource (e.g., static files).
     * Returns NOT_FOUND status with resource path details.
     */
    @Override
    protected ResponseEntity<Object> handleNoResourceFoundException(
            NoResourceFoundException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String notFoundMessage = String.format("No resource found for %s %s", ex.getHttpMethod(), ex.getResourcePath());
        log.warn(notFoundMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.NOT_FOUND.getCode())
                .message(AppErrorCode.NOT_FOUND.getMessage())
                .messages(List.of(notFoundMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    /**
     * Handle NoHandlerFoundException - thrown when Spring cannot find a handler for the request.
     * Returns NOT_FOUND status with request URL details.
     */
    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(
            NoHandlerFoundException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String notFoundMessage = String.format("No handler found for %s %s", ex.getHttpMethod(), ex.getRequestURL());
        log.warn(notFoundMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.NOT_FOUND.getCode())
                .message(AppErrorCode.NOT_FOUND.getMessage())
                .messages(List.of(notFoundMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    /**
     * Handle HttpRequestMethodNotSupportedException - thrown when a request is made with an unsupported HTTP method.
     * For example, when a POST request is made to an endpoint that only supports GET.
     * Returns METHOD_NOT_ALLOWED (405) status with details about the unsupported method and supported methods.
     */
    @Override
    protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(
            HttpRequestMethodNotSupportedException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String supportedMethods = ex.getSupportedHttpMethods() != null
                ? ex.getSupportedHttpMethods().toString()
                : "N/A";
        String errorMessage = String.format("Method '%s' is not supported. Supported methods: %s",
                ex.getMethod(), supportedMethods);

        log.warn("HTTP method not supported: {}", errorMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.METHOD_NOT_ALLOWED.getCode())
                .message(AppErrorCode.METHOD_NOT_ALLOWED.getMessage())
                .messages(List.of(errorMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.METHOD_NOT_ALLOWED);
    }

    /**
     * Handle HttpMediaTypeNotSupportedException - thrown when the Content-Type of the request is not supported.
     * For example, when sending XML to an endpoint that only accepts JSON.
     * Returns UNSUPPORTED_MEDIA_TYPE (415) status with details about the unsupported media type.
     */
    @Override
    protected ResponseEntity<Object> handleHttpMediaTypeNotSupported(
            HttpMediaTypeNotSupportedException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String supportedTypes = ex.getSupportedMediaTypes() != null
                ? ex.getSupportedMediaTypes().toString()
                : "N/A";
        String errorMessage = String.format("Media type '%s' is not supported. Supported media types: %s",
                ex.getContentType(), supportedTypes);

        log.warn("Unsupported media type: {}", errorMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.UNSUPPORTED_MEDIA_TYPE.getCode())
                .message(AppErrorCode.UNSUPPORTED_MEDIA_TYPE.getMessage())
                .messages(List.of(errorMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.UNSUPPORTED_MEDIA_TYPE);
    }

    /**
     * Handle HttpMediaTypeNotAcceptableException - thrown when the Accept header cannot be satisfied.
     * For example, when the client requests XML but the server can only produce JSON.
     * Returns NOT_ACCEPTABLE (406) status with details about acceptable media types.
     */
    @Override
    protected ResponseEntity<Object> handleHttpMediaTypeNotAcceptable(
            HttpMediaTypeNotAcceptableException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String supportedTypes = ex.getSupportedMediaTypes() != null
                ? ex.getSupportedMediaTypes().toString()
                : "N/A";
        String errorMessage = String.format("Requested media type is not acceptable. Supported media types: %s",
                supportedTypes);

        log.warn("Media type not acceptable: {}", errorMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.MEDIA_TYPE_NOT_ACCEPTABLE.getCode())
                .message(AppErrorCode.MEDIA_TYPE_NOT_ACCEPTABLE.getMessage())
                .messages(List.of(errorMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.NOT_ACCEPTABLE);
    }

    /**
     * Handle MissingServletRequestParameterException - thrown when a required request parameter is missing.
     * For example, when a @RequestParam(required=true) parameter is not provided in the request.
     * Returns BAD_REQUEST (400) status with details about the missing parameter.
     */
    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String errorMessage = String.format("Required parameter '%s' of type '%s' is missing",
                ex.getParameterName(), ex.getParameterType());

        log.warn("Missing request parameter: {}", errorMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Missing request parameter")
                .messages(List.of(errorMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle ServletRequestBindingException - thrown when request binding fails.
     * This includes missing request headers, missing cookies, or other binding failures.
     * Returns BAD_REQUEST (400) status with details about the binding error.
     */
    @Override
    protected ResponseEntity<Object> handleServletRequestBindingException(
            ServletRequestBindingException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        log.warn("Request binding error: {}", ex.getMessage());

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Request binding error")
                .messages(List.of(ex.getMessage()))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle TypeMismatchException - thrown when a request parameter or path variable has the wrong type.
     * For example, when a String is provided for an Integer parameter, or an invalid enum value is used.
     * Returns BAD_REQUEST (400) status with details about the type mismatch.
     */
    @Override
    protected ResponseEntity<Object> handleTypeMismatch(
            TypeMismatchException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String requiredType = ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "unknown";
        String errorMessage = String.format("Type mismatch for parameter '%s': expected %s but got '%s'",
                ex.getPropertyName(), requiredType, ex.getValue());

        log.warn("Type mismatch: {}", errorMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Type mismatch")
                .messages(List.of(errorMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle MissingPathVariableException - thrown when a required path variable is missing.
     * This typically indicates a configuration error in the controller mapping.
     * Returns BAD_REQUEST (400) status with details about the missing path variable.
     */
    @Override
    protected ResponseEntity<Object> handleMissingPathVariable(
            MissingPathVariableException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String errorMessage = String.format("Required path variable '%s' is missing", ex.getVariableName());

        log.warn("Missing path variable: {}", errorMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.BAD_REQUEST.getCode())
                .message("Missing path variable")
                .messages(List.of(errorMessage))
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ServiceResponse<?>> handleAccessDeniedException(AccessDeniedException ex) {
        log.warn("Access denied: {}", ex.getMessage());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.FORBIDDEN.getCode())
                .message(AppErrorCode.FORBIDDEN.getMessage())
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.FORBIDDEN);
    }

    /**
     * Handle all other uncaught exceptions.
     * Returns INTERNAL_SERVER_ERROR status with generic error message.
     * Logs the full exception for debugging purposes.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ServiceResponse<?>> handleAllExceptions(Exception ex) {
        log.error("Unhandled exception occurred", ex);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.INTERNAL_SERVER_ERROR.getCode())
                .message(AppErrorCode.INTERNAL_SERVER_ERROR.getMessage())
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

