package vinsf.miniapp.mapper;

import vinsf.miniapp.constant.KeyType;
import vinsf.miniapp.dto.*;
import vinsf.miniapp.model.MiniApp;
import org.mapstruct.*;

import java.util.Map;
import java.util.Set;

/**
 * MapStruct mapper for MiniApp entity and DTO
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@Mapper(componentModel = "spring")
public interface MiniAppMapper {

    /**
     * Convert entity to DTO
     *
     * @param entity the MiniApp entity
     * @return MiniAppDto
     */
    MiniAppDto toDto(MiniApp entity);

    /**
     * Convert create DTO to DTO
     *
     * @param createDto the MiniAppCreateDto
     * @param secretEncrypted the encrypted secret
     * @param keys the signing keys
     * @return MiniApp DTO
     */
    MiniAppDto toDto(MiniAppCreateDto createDto, EncryptedSecret secretEncrypted, Map<KeyType, Set<MiniAppKey>> keys);

    /**
     * Convert update DTO to DTO
     *
     * @param updateDto the MiniAppUpdateDto
     * @return MiniApp DTO
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "secretEncrypted", ignore = true),
            @Mapping(target = "keys", ignore = true),
            @Mapping(target = "webhooks", ignore = true),
            @Mapping(target = "status", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "updatedBy", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "updatedOn", ignore = true),
            @Mapping(target = "active", ignore = true)
    })
    MiniAppDto toDto(MiniAppUpdateDto updateDto);

    /**
     * Convert DTO to entity
     *
     * @param dto the MiniAppDto
     * @return MiniApp entity
     */
    MiniApp toEntity(MiniAppDto dto);

    /**
     * Update entity from DTO (for partial updates)
     * Only non-null fields in DTO will be copied to entity
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget MiniApp entity, MiniAppDto dto);

    /**
     * Update entity from DTO (for full updates)
     *
     * @param dto    the source DTO
     * @param entity the target entity to update
     */
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "createdOn", ignore = true),
            @Mapping(target = "createdBy", ignore = true),
            @Mapping(target = "version", ignore = true),
            @Mapping(target = "updatedOn", ignore = true)
    })
    void update(@MappingTarget MiniApp entity, MiniAppDto dto);
}

