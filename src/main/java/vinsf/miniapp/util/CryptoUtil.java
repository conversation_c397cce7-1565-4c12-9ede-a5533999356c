package vinsf.miniapp.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import vinsf.miniapp.constant.AppErrorCode;
import vinsf.miniapp.dto.ServiceResponse;
import vinsf.miniapp.exception.BusinessLogicException;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPublicKey;
import java.util.Base64;

/**
 * Utility class for cryptographic operations
 * Provides methods for generating secrets, RSA keypairs, and other crypto operations
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CryptoUtil {

    /**
     * Generates a secure random secret
     *
     * @param byteLength the length of the secret in bytes
     * @return secure random secret string
     */
    public static String generateSecret(int byteLength) {
        try {
            log.debug("Generating secret with length: {}", byteLength);
            SecureRandom secureRandom = new SecureRandom();
            byte[] secretBytes = new byte[byteLength];
            secureRandom.nextBytes(secretBytes);

            return Base64.getUrlEncoder().withoutPadding().encodeToString(secretBytes);
        } catch (Exception e) {
            log.error("Failed to generate secret", e);
            throw new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.MINIAPP_SECRET_GENERATION_FAILED)
            );
        }
    }

    /**
     * Generates an RSA-2048 keypair
     *
     * @return KeyPair containing public and private keys
     */
    public static KeyPair generateKeypair(String algorithm, int keySize) {
        try {
            log.debug("Generating {} keypair with size {}", algorithm, keySize);
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(algorithm);
            keyPairGenerator.initialize(keySize);

            return keyPairGenerator.generateKeyPair();

        } catch (Exception e) {
            log.error("Failed to generate {} keypair", algorithm, e);
            throw new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.MINIAPP_KEYPAIR_GENERATION_FAILED)
            );
        }
    }

    /**
     * Encodes a public key to Base64 PEM format
     *
     * @param publicKey the public key to encode
     * @return Base64 encoded public key
     */
    public static String encodePublicKey(PublicKey publicKey) {
        byte[] encoded = publicKey.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    /**
     * Encodes a private key to Base64 PEM format
     *
     * @param privateKey the private key to encode
     * @return Base64 encoded private key
     */
    public static String encodePrivateKey(PrivateKey privateKey) {
        byte[] encoded = privateKey.getEncoded();
        return Base64.getEncoder().encodeToString(encoded);
    }

    /**
     * Generates a unique key ID (kid) for signing keys
     *
     * @return unique key ID
     */
    public static String generateRsaKeyId(RSAPublicKey pub) {
        try {
            log.debug("Generating RSA key ID");
            // JWK fields: e, kty, n (lexicographic order of member names)
            String e = b64u(unsigned(pub.getPublicExponent()));
            String n = b64u(unsigned(pub.getModulus()));
            String jwkCanonical = "{\"e\":\"" + e + "\",\"kty\":\"RSA\",\"n\":\"" + n + "\"}";

            byte[] digest = sha256(jwkCanonical.getBytes(StandardCharsets.UTF_8));
            return b64u(digest);
        } catch (Exception e) {
            log.error("Failed to generate RSA key ID", e);
            throw new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.MINIAPP_KEYPAIR_GENERATION_FAILED)
            );
        }
    }

    private static byte[] unsigned(BigInteger bi) {
        byte[] bytes = bi.toByteArray();
        // strip leading 0x00 sign byte if present
        if (bytes.length > 1 && bytes[0] == 0x00) {
            byte[] tmp = new byte[bytes.length - 1];
            System.arraycopy(bytes, 1, tmp, 0, tmp.length);
            return tmp;
        }
        return bytes;
    }

    private static byte[] sha256(byte[] data) {
        try {
            return MessageDigest.getInstance("SHA-256").digest(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String b64u(byte[] data) {
        return Base64.getUrlEncoder().withoutPadding().encodeToString(data);
    }
}

