package vinsf.miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import vinsf.miniapp.constant.ImageType;
import vinsf.miniapp.util.CStringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Validator for {@link ValidateImageLocaleMap} annotation.
 * Validates that all values in a Map&lt;String, String&gt; are valid image URLs
 * with file extensions matching the supported types.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class ImageLocaleMapValidator implements ConstraintValidator<ValidateImageLocaleMap, Map<String, String>> {

    private Set<ImageType> supportedTypes;

    @Override
    public void initialize(ValidateImageLocaleMap constraintAnnotation) {
        // Convert supported types to lowercase for case-insensitive comparison
        this.supportedTypes = Arrays.stream(constraintAnnotation.supportedTypes())
                .collect(Collectors.toSet());
    }

    @Override
    public boolean isValid(Map<String, String> value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Validate each URL in the map
        for (Map.Entry<String, String> entry : value.entrySet()) {
            String imageUrl = entry.getValue();
            
            // Skip null values
            if (imageUrl == null) {
                continue;
            }

            // Validate URL format
            if (!CStringUtils.isValidUrl(imageUrl)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        String.format("Invalid URL format for locale '%s': %s", entry.getKey(), imageUrl)
                ).addConstraintViolation();
                return false;
            }

            // Validate image file extension
            if (!CStringUtils.hasValidImageExtension(imageUrl, supportedTypes)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        String.format("Invalid image extension for locale '%s': %s. Supported types: %s",
                                entry.getKey(), imageUrl, supportedTypes.stream().map(ImageType::getExt).collect(Collectors
                                        .joining(", ")))
                ).addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}

