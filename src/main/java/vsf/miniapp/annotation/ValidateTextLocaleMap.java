package vfs.miniapp.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * Validates a Map&lt;String, String&gt; where keys are locale codes and values are text content.
 * Checks that all map values have text length within the specified range.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@Documented
@Constraint(validatedBy = TextLocaleMapValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateTextLocaleMap {

    /**
     * Error message when validation fails
     */
    String message() default "Invalid text length in locale map";

    /**
     * Validation groups
     */
    Class<?>[] groups() default {};

    /**
     * Payload for clients
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * Minimum length for text values (inclusive)
     */
    int minLength() default 0;

    /**
     * Maximum length for text values (inclusive)
     */
    int maxLength() default Integer.MAX_VALUE;
}

