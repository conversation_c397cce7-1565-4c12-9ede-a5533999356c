package vfs.miniapp.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * Validates single domain name (String) or collection of domain names (List&lt;String&gt;, Set&lt;String&gt;, etc.).
 * Checks that each domain follows valid domain name format (RFC 1035/1123):
 * <ul>
 *   <li>Contains only alphanumeric characters, hyphens, and dots</li>
 *   <li>Does not start or end with hyphen</li>
 *   <li>Each label is 1-63 characters</li>
 *   <li>Total length does not exceed 253 characters</li>
 *   <li>Has valid TLD</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@Documented
@Constraint(validatedBy = {DomainsValidator.class, DomainsCollectionValidator.class})
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateDomains {

    /**
     * Error message when validation fails
     */
    String message() default "Invalid domain name";

    /**
     * Validation groups
     */
    Class<?>[] groups() default {};

    /**
     * Payload for clients
     */
    Class<? extends Payload>[] payload() default {};
}

