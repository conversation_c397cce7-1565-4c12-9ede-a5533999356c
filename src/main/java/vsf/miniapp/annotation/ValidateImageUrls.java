package vfs.miniapp.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import miniapp.constant.ImageType;

import java.lang.annotation.*;

/**
 * Validates single image URL (String) or collection of image URLs (List&lt;String&gt;, Set&lt;String&gt;, etc.).
 * Checks that each URL is a valid HTTP/HTTPS URL and ends with one of the supported image file extensions.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@Documented
@Constraint(validatedBy = {ImageUrlsValidator.class, ImageUrlsCollectionValidator.class})
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateImageUrls {

    /**
     * Error message when validation fails
     */
    String message() default "Invalid image URL";

    /**
     * Validation groups
     */
    Class<?>[] groups() default {};

    /**
     * Payload for clients
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * Supported image file extensions (case-insensitive)
     */
    ImageType[] supportedTypes() default {ImageType.PNG, ImageType.JPG, ImageType.JPEG, ImageType.GIF, ImageType.SVG, ImageType.WEBP};
}

