package vfs.miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import miniapp.util.CStringUtils;

/**
 * Validator for {@link ValidateIPs} annotation for single IP address (String).
 * Validates IPv4, IPv6, and CIDR notation for both.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class IPsValidator implements ConstraintValidator<ValidateIPs, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Trim whitespace
        String trimmedValue = value.trim();
        if (trimmedValue.isEmpty()) {
            return false;
        }

        return CStringUtils.isValidIpAddress(trimmedValue);
    }
}

