package vfs.miniapp.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import miniapp.constant.ImageType;

import java.lang.annotation.*;

/**
 * Validates a Map&lt;String, String&gt; where keys are locale codes and values are image URLs.
 * Checks that all map values are valid image URLs with file extensions matching the supported types.
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@Documented
@Constraint(validatedBy = ImageLocaleMapValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateImageLocaleMap {

    /**
     * Error message when validation fails
     */
    String message() default "Invalid image URL in locale map";

    /**
     * Validation groups
     */
    Class<?>[] groups() default {};

    /**
     * Payload for clients
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * Supported image file extensions (case-insensitive)
     */
    ImageType[] supportedTypes() default {ImageType.PNG, ImageType.JPG, ImageType.JPEG, ImageType.GIF, ImageType.SVG, ImageType.WEBP};
}

