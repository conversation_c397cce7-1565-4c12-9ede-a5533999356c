package vfs.miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import miniapp.util.CStringUtils;

import java.util.Collection;

/**
 * Validator for {@link ValidateDomains} annotation for collection of domain names.
 * Validates each domain name in the collection according to RFC 1035/1123.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class DomainsCollectionValidator implements ConstraintValidator<ValidateDomains, Collection<String>> {

    @Override
    public boolean isValid(Collection<String> value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Validate each domain in the collection
        for (String domain : value) {
            // Skip null values
            if (domain == null) {
                continue;
            }

            String trimmedDomain = domain.trim();
            if (trimmedDomain.isEmpty()) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        "Empty domain name found in collection"
                ).addConstraintViolation();
                return false;
            }

            if (!CStringUtils.isValidDomain(trimmedDomain)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        String.format("Invalid domain name: %s", domain)
                ).addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}

