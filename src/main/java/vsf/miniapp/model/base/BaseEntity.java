package vfs.miniapp.model.base;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

@MappedSuperclass
@Getter
@Setter
@ToString
@FieldNameConstants
public abstract class BaseEntity {

    @Version
    private Integer version;

    @CreatedBy
    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @LastModifiedBy
    @Column(name = "updated_by")
    private String updatedBy;

    @CreatedDate
    @Column(name = "created_on", updatable = false, columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime createdOn;

    @LastModifiedDate
    @Column(name = "updated_on", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime updatedOn;

    @Column(name = "active", nullable = false, columnDefinition = "boolean default true")
    private Boolean active;
}
