package vfs.miniapp.model;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import miniapp.constant.KeyType;
import miniapp.constant.MiniAppPermission;
import miniapp.constant.MiniAppStatus;
import miniapp.constant.MiniAppType;
import miniapp.converter.MiniAppStatusConverter;
import miniapp.dto.EncryptedSecret;
import miniapp.dto.MiniAppKey;
import miniapp.dto.MiniAppUserScope;
import miniapp.dto.MiniAppWebhook;
import miniapp.model.base.BaseEntityUUID;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapps")
@FieldNameConstants
public class MiniApp extends BaseEntityUUID {
    public static final String ENTITY_NAME = "MINIAPP";

    @Column(name = "code", length = 64, nullable = false, updatable = false)
    private String code;

    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "secret_encrypted", columnDefinition = "jsonb")
    private EncryptedSecret secretEncrypted; // stored via KMS

    @Column(name = "category_id")
    private Long categoryId; // danh mục của miniapp

    @Column(name = "org_code")
    private Long orgCode; // đơn vị phát triển (PnL/Partner)

    @Column(name = "type", length = 16)
    private MiniAppType type;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "display_names", columnDefinition = "jsonb")
    private Map<String, String> displayNames; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "descriptions", columnDefinition = "jsonb")
    private Map<String, String> descriptions; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "logos", columnDefinition = "jsonb", nullable = false)
    private Map<String, String> logos; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "banners", columnDefinition = "jsonb")
    private Map<String, String> banners; // {"vi": "...", "en":"..."}

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "showcase", columnDefinition = "jsonb")
    private Set<String> showcase;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "scopes", columnDefinition = "jsonb")
    private Set<MiniAppUserScope> scopes; // ["public_profile","phone","email","ekyc"]

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "permissions", columnDefinition = "jsonb")
    private Set<MiniAppPermission> permissions;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "whitelist_ips", columnDefinition = "jsonb")
    private Set<String> whitelistIps; // ["*******/32", ...]

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "whitelist_domains", columnDefinition = "jsonb")
    private Set<String> whitelistDomains; // ["api.partner.com", ...]

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "webhooks", columnDefinition = "jsonb")
    private Set<MiniAppWebhook> webhooks;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "keys", columnDefinition = "jsonb")
    private Map<KeyType, Set<MiniAppKey>> keys;

    @Convert(converter = MiniAppStatusConverter.class)
    @Column(name = "status", length = 16)
    private MiniAppStatus status;
}
