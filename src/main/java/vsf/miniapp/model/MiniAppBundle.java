package vfs.miniapp.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import miniapp.constant.BundleStatus;
import miniapp.model.base.BaseEntityLong;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_bundles")
public class MiniAppBundle extends BaseEntityLong {

    @Column(name = "miniapp_id", nullable = false)
    private String miniappId;

    @Column(name = "semver", length = 16, nullable = false, updatable = false)
    private String semver;

    @Column(name = "build_no",nullable = false, updatable = false)
    private Integer buildNo;

    @Column(name = "status", length = 16)
    private BundleStatus status;

    @Column(name = "storage_url", length = 1024, nullable = false)
    private String storageUrl;

    @Column(name = "checksum", length = 128, nullable = false)
    private String checksum;

    @Column(name = "size_bytes")
    private Long sizeBytes;

    @Column(name = "uploader", length = 100)
    private String uploader;

    @Column(name = "upload_at")
    private Long uploadAt;

    @Column(name = "scan_report", columnDefinition = "text")
    private String scanReport;
}
