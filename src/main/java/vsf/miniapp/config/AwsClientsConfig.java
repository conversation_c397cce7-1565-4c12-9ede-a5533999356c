package vfs.miniapp.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kms.KmsClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;

import java.net.URI;

@Configuration
public class AwsClientsConfig {

    @Value("${aws.region:}")
    private String region;

    @Value("${aws.localstack.endpoint:}")
    private String localstackEndpoint;

    @Value("${aws.s3.forcePathStyle:false}")
    private boolean s3ForcePathStyle;

    @Value("${aws.s3.accessKey:}")
    private String s3AccessKey;

    @Value("${aws.s3.secretKey:}")
    private String s3SecretKey;

    @Bean(destroyMethod = "close")
    KmsClient kmsClient() {
        var b = KmsClient.builder().region(Region.of(region))
                .credentialsProvider(DefaultCredentialsProvider.create());
        if (localstackEndpoint != null) {
            b = b.endpointOverride(URI.create(localstackEndpoint));
        }
        return b.build();
    }

    @Bean(destroyMethod = "close")
    S3Client s3Client() {
        var builder = S3Client.builder()
                .region(Region.of(region));

        if (StringUtils.hasText(s3AccessKey) && StringUtils.hasText(s3SecretKey)) {
            builder.credentialsProvider(() ->
                    AwsBasicCredentials.create(s3AccessKey, s3SecretKey)
            );
        } else {
            builder.credentialsProvider(DefaultCredentialsProvider.create());
        }

        if (StringUtils.hasText(localstackEndpoint)) {
            builder.endpointOverride(URI.create(localstackEndpoint))
                    .serviceConfiguration(S3Configuration.builder()
                            .pathStyleAccessEnabled(s3ForcePathStyle)
                            .build());
        }

        return builder.build();
    }
}
