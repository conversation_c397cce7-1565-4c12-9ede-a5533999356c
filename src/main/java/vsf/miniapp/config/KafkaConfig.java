package vfs.miniapp.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.RoundRobinAssignor;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConfig {

    //    private static final String KAFKA_PROPERTIES = "kafka.properties.";
    private static final String SASL_MECHANISM = "sasl.mechanism";
    private static final String BOOTSTRAP_SERVERS = "bootstrap.servers";
    private static final String SASL_JAAS_CONFIG = "sasl.jaas.config";
    private static final String SECURITY_PROTOCOL = "security.protocol";
    private static final String BASIC_AUTH_CREDENTIALS_SOURCE = "basic.auth.credentials.source";
    private static final String SCHEMA_REGISTRY_BASIC_AUTH_USER_INFO = "schema.registry.basic.auth.user.info";
    private static final String SCHEMA_REGISTRY_URL = "schema.registry.url";
    private static final String AUTO_OFFSET_RESET = "auto.offset.reset";
    private static final String GROUP_ID = "group.id";

    @Value("${kafka.properties.sasl.mechanism}")
    private String saslMechanism;

    @Value("${kafka.properties.bootstrap.servers}")
    private String bootstrapServer;

    @Value("${kafka.properties.sasl.jaas.config}")
    private String jaslJaasConfig;

    @Value("${kafka.properties.security.protocol}")
    private String securityProtocol;

    @Value("${kafka.properties.basic.auth.credentials.source}")
    private String credentialSource;

    @Value("${kafka.properties.schema.registry.basic.auth.user.info}")
    private String registryUserInfo;

    @Value("${kafka.properties.schema.registry.url}")
    private String registryUrl;

    @Value("${kafka.properties.group.id:default-group-id}")
    private String groupId;

    @Value("${kafka.properties.auto.offset.reset}")
    private String offsetReset;

    @Value("${kafka.producer.properties.linger.ms:1}")
    private int producerLingerMs;

    @Value("${kafka.consumer.default.max_poll_records:1}")
    private int defaultMaxPollRecords;

    @Value("${kafka.consumer.default.consumer_count:1}")
    private int defaultConsumerCount;

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> props = mapConfig();
        props.put(ProducerConfig.LINGER_MS_CONFIG, producerLingerMs);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return new DefaultKafkaProducerFactory<>(props);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> props = mapConfig();
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, defaultMaxPollRecords);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        return new DefaultKafkaConsumerFactory<>(props);
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> autoCommitKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();

        // Default is AckMode.RECORD, which uses auto-commit for auto-commit consumer
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(defaultConsumerCount);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.RECORD);
        return factory;
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> manualCommitKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();


        // Manual acknowledgment
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(defaultConsumerCount);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }

    private Map<String, Object> mapConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(SASL_MECHANISM, saslMechanism);
        props.put(BOOTSTRAP_SERVERS, bootstrapServer);
        props.put(SASL_JAAS_CONFIG, jaslJaasConfig);
        props.put(SECURITY_PROTOCOL, securityProtocol);
        props.put(BASIC_AUTH_CREDENTIALS_SOURCE, credentialSource);
        if (StringUtils.hasText(registryUserInfo)) {
            props.put(SCHEMA_REGISTRY_BASIC_AUTH_USER_INFO, registryUserInfo);
        }
        props.put(SCHEMA_REGISTRY_URL, registryUrl);
        if (StringUtils.hasText(offsetReset)) {
            props.put(AUTO_OFFSET_RESET, offsetReset);
        }
        props.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG, RoundRobinAssignor.class.getName());
        return props;
    }
}
