package vfs.miniapp.service;

import miniapp.dto.MiniAppCreateDto;
import miniapp.dto.MiniAppDto;
import miniapp.dto.MiniAppFilter;
import miniapp.dto.MiniAppUpdateDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Business Service interface for MiniApp Management operations
 * Orchestrates MiniApp entity service and handles encryption/decryption with AWS KMS
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public interface MiniAppManagementService {

    /**
     * Create a new MiniApp with auto-generated secrets and signing keys
     * Auto-generates:
     * - secret (UUID) and encrypts using AWS KMS
     * - RSA-2048 signing keypair, encrypts privateKey using AWS KMS
     * - webhookSecret (UUID) for each webhook and encrypts using AWS KMS
     *
     * @param dto the MiniApp creation data
     * @return created MiniAppDto
     */
    MiniAppDto create(MiniAppCreateDto dto);

    /**
     * Update an existing MiniApp
     * Does NOT allow updating code, secret, signingKey, or webhookSecret
     *
     * @param id  the MiniApp ID
     * @param dto the update data
     * @return updated MiniAppDto with ServiceResponse wrapper
     */
    MiniAppDto update(String id, MiniAppUpdateDto dto);

    /**
     * Delete MiniApp by ID (soft delete)
     *
     * @param id the MiniApp ID
     */
    void delete(String id);

    /**
     * Change MiniApp status
     *
     * @param id     the MiniApp ID
     * @param status the new status
     * @return updated MiniAppDto
     */
    MiniAppDto changeStatus(String id, String status);

    /**
     * Get MiniApp by ID
     *
     * @param id the MiniApp ID
     * @return MiniAppDto with ServiceResponse wrapper
     */
    MiniAppDto getById(String id);

    /**
     * Search MiniApps with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppDto
     */
    Page<MiniAppDto> search(MiniAppFilter filter, Pageable pageable);

    /**
     * Retrieve decrypted secret for a MiniApp
     *
     * @param id the MiniApp ID
     * @return decrypted secret
     */
    String retrieveSecret(String id);

    /**
     * Regenerate MiniApp secret
     * Only allowed when MiniApp is not ACTIVE
     *
     * @param id the MiniApp ID
     * @return updated MiniAppDto
     */
    MiniAppDto regenerateSecret(String id);

    /**
     * Rotate MiniApp signing key
     *
     * @param id the MiniApp ID
     * @return updated MiniAppDto
     */
    MiniAppDto rotateSigningKey(String id);
}

