package vfs.miniapp.service;

import miniapp.dto.EncryptedSecret;

public interface KmsService {

    /**
     * Encrypts plaintext using envelope encryption with KMS
     *
     * @param plaintext the plaintext to encrypt
     * @return EncryptedSecret containing encrypted data and metadata
     */
    EncryptedSecret encrypt(String plaintext);

    /**
     * Decrypts encrypted data using KMS
     *
     * @param encryptedSecret the encrypted secret containing ciphertext and metadata
     * @return decrypted plaintext
     */
    String decrypt(EncryptedSecret encryptedSecret);
}
