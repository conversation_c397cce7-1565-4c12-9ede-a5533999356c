package vfs.miniapp.repository;

import miniapp.model.MiniApp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository for MiniApp entity
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@Repository
public interface MiniAppRepository extends JpaRepository<MiniApp, String>, JpaSpecificationExecutor<MiniApp> {

    /**
     * Find MiniApp by code
     *
     * @param code the unique code
     * @return Optional of MiniApp
     */
    Optional<MiniApp> findByCode(String code);

    /**
     * Check if MiniApp exists by code
     *
     * @param code the unique code
     * @return true if exists
     */
    boolean existsByCode(String code);
}

