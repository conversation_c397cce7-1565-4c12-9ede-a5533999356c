package vfs.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImageType {
    PNG("png"),
    JPG("jpg"),
    JPEG("jpeg"),
    GIF("gif"),
    SVG("svg"),
    WEBP("webp")
    ;

    private final String ext;

    @JsonCreator
    public static ImageType fromExt(String value) {
        for (ImageType type : ImageType.values()) {
            if (type.ext.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid ImageType value: " + value);
    }

    @JsonValue
    public String toExt() {
        return ext;
    }
}
