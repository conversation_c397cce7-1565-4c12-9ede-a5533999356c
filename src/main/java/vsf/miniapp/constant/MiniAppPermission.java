package vfs.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MiniAppPermission {
    LOCATION("location"),
    CAMERA("camera"),
    MICROPHONE("microphone"),
    <PERSON><PERSON>AG<PERSON>("storage"),
    B<PERSON>UETOOTH("bluetooth"),
    CONTACT("contact"),
    CALENDAR("calendar"),
    SMS("sms"),
    DIAL("dial"),
    NOTIFICATION("notification"),
    USER_PROFILE("user_profile"),
    PAYMENT("payment"),
    LOYALTY("loyalty")
    ;

    private final String code;

    @JsonValue
    public String toCode() {
        return code;
    }

    public static MiniAppPermission fromCode(String name) {
        for (MiniAppPermission scope : MiniAppPermission.values()) {
            if (scope.code.equalsIgnoreCase(name)) {
                return scope;
            }
        }
        throw new IllegalArgumentException("Invalid MiniAppPermission value: " + name);
    }


}
