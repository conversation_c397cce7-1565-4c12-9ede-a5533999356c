package vfs.miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BundleStatus {
    DRAFT("DRAFT"), // after upload
    DEVELOP("DEVELOP"), // Deploy Develop: after CI build + scan + attestation
    TESTING("TESTING"), // Deploy Testing: after CI build + scan + attestation
    SUBMITTED("SUBMITTED"), // after tester verify passed => Admin submit for review
    APPROVED("APPROVED"), // after reviewer approved
    REJECTED("REJECTED"); // rejected by reviewer or admin after tester verify failed

    private final String code;

    @JsonCreator
    public static BundleStatus fromCode(String value) {
        for (BundleStatus status : BundleStatus.values()) {
            if (status.code.equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid BundleStatus value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
