package vfs.miniapp.controller;


import lombok.RequiredArgsConstructor;
import miniapp.dto.ServiceResponse;
import miniapp.util.JsonUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/hello")
@RequiredArgsConstructor
public class HelloController {

    private final JsonUtil jsonUtil;

    @GetMapping("/world")
    public ServiceResponse<String> helloWorld() {
        return ServiceResponse.success("Hello, World!");
    }

    @GetMapping("/world/json")
    public ServiceResponse<?> helloWorldJsonStr() {
        return ServiceResponse.success(Map.of("first", "hello", "second", "world"));
    }

}
