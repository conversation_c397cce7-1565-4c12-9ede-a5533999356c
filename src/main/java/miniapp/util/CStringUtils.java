package miniapp.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import miniapp.constant.ImageType;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Common String utilities for validation across the project.
 * <p>
 * Centralizes frequently used validation helpers to avoid duplication in validators:
 * - HTTP/HTTPS URL validation
 * - Image URL validation with supported extensions
 * - IP address and CIDR validation (IPv4/IPv6)
 * <p>
 * Usage: static methods only.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CStringUtils {
    // ========================= Patterns ========================= //
    private static final Pattern IPV4_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );

    private static final Pattern IPV4_CIDR_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[12]?[0-9])$"
    );

    private static final Pattern IPV6_PATTERN = Pattern.compile(
            "^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|" +
                    "([0-9a-fA-F]{1,4}:){1,7}:|" +
                    "([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|" +
                    "([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|" +
                    "([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|" +
                    "([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|" +
                    "([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|" +
                    "[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|" +
                    ":((:[0-9a-fA-F]{1,4}){1,7}|:)|" +
                    "fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|" +
                    "::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|" +
                    "([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$"
    );

    private static final Pattern IPV6_CIDR_PATTERN = Pattern.compile(
            "^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|" +
                    "([0-9a-fA-F]{1,4}:){1,7}:|" +
                    "([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|" +
                    "([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|" +
                    "([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|" +
                    "([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|" +
                    "([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|" +
                    "[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|" +
                    ":((:[0-9a-fA-F]{1,4}){1,7}|:))/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$"
    );

    // Domain label pattern: alphanumeric and hyphens, not starting or ending with hyphen
    private static final Pattern LABEL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$"
    );

    // Common TLDs pattern (not exhaustive, but covers most common cases)
    private static final Pattern TLD_PATTERN = Pattern.compile(
            "^[a-zA-Z]{2,}$"
    );

    // ========================= Constants ========================= //
    private static final int MAX_DOMAIN_LENGTH = 253;
    private static final int MAX_LABEL_LENGTH = 63;

    // ========================= Methods ========================= //
    /**
     * Checks whether the given string is a valid HTTP or HTTPS URL.
     *
     * @param urlString the URL string to validate
     * @return true if valid http/https URL; false otherwise
     */
    public static boolean isValidUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            String protocol = url.getProtocol();
            return "http".equalsIgnoreCase(protocol) || "https".equalsIgnoreCase(protocol);
        } catch (MalformedURLException e) {
            return false;
        }
    }

    /**
     * Checks whether the given string is a valid image URL.
     *
     * @param urlString the URL string to validate
     * @return true if valid image URL; false otherwise
     */
    public static boolean isValidImageExtension(String urlString) {
        return hasValidImageExtension(urlString, Set.of(ImageType.values()));
    }

    /**
     * Determines if an URL points to a resource with an extension contained in supportedTypes.
     * Query and fragment components (if any) are ignored for the purpose of extracting the extension.
     *
     * @param urlString URL string
     * @param supportedTypes set of ImageType (e.g., ImageType.PNG, ImageType.JPG)
     * @return true if URL ends with a supported extension; false otherwise
     */
    public static boolean hasValidImageExtension(String urlString, Set<ImageType> supportedTypes) {
        String lowerUrl = urlString.toLowerCase();

        int endIndex = lowerUrl.length();
        int queryIndex = lowerUrl.indexOf('?');
        if (queryIndex >= 0) {
            endIndex = queryIndex;
        }
        int fragmentIndex = lowerUrl.indexOf('#');
        if (fragmentIndex >= 0) {
            endIndex = Math.min(endIndex, fragmentIndex);
        }
        String pathPart = lowerUrl.substring(0, endIndex);

        int lastDotIndex = pathPart.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == pathPart.length() - 1) {
            return false;
        }

        String extension = pathPart.substring(lastDotIndex + 1);
        return supportedTypes.stream().anyMatch(type -> type.getExt().equalsIgnoreCase(extension));
    }

    /**
     * Checks whether the given string is a valid image URL with default supported types.
     *
     * @param urlString the URL string to validate
     * @return true if valid image URL; false otherwise
     */
    public static boolean isValidImageUrl(String urlString) {
        return isValidImageUrl(urlString, Set.of(ImageType.values()));
    }

    /**
     * Validates whether the given string is a valid HTTP/HTTPS URL and ends with a supported image extension.
     *
     * @param urlString URL string
     * @param supportedTypes set of ImageType (e.g., ImageType.PNG, ImageType.JPG)
     * @return true if valid image URL per current rules
     */
    public static boolean isValidImageUrl(String urlString, Set<ImageType> supportedTypes) {
        if (!isValidUrl(urlString)) {
            return false;
        }
        return hasValidImageExtension(urlString, supportedTypes);
    }

    /**
     * Validates whether the given string is a valid IP address or CIDR notation (IPv4/IPv6).
     * Mirrors existing project validator behavior.
     *
     * @param ip the input string
     * @return true if valid IP or CIDR notation; false otherwise
     */
    public static boolean isValidIpAddress(String ip) {
        return IPV4_PATTERN.matcher(ip).matches() ||
               IPV4_CIDR_PATTERN.matcher(ip).matches() ||
               IPV6_PATTERN.matcher(ip).matches() ||
               IPV6_CIDR_PATTERN.matcher(ip).matches();
    }



    /**
     * Validates whether the given string is a valid domain name according to RFC 1035/1123.
     * Mirrors existing project validator behavior.
     *
     * @param domain the input string
     * @return true if valid domain name; false otherwise
     */
    public static boolean isValidDomain(String domain) {
        // Check total length
        if (domain.length() > MAX_DOMAIN_LENGTH) {
            return false;
        }

        // Domain should not start or end with dot
        if (domain.startsWith(".") || domain.endsWith(".")) {
            return false;
        }

        // Split into labels
        String[] labels = domain.split("\\.");

        // Domain must have at least 2 labels (e.g., example.com)
        if (labels.length < 2) {
            return false;
        }

        // Validate each label
        for (String label : labels) {
            // Check label length
            if (label.isEmpty() || label.length() > MAX_LABEL_LENGTH) {
                return false;
            }

            // Validate label format
            if (!LABEL_PATTERN.matcher(label).matches()) {
                return false;
            }
        }

        // Validate TLD (last label)
        String tld = labels[labels.length - 1];
        return TLD_PATTERN.matcher(tld).matches();
    }
}
