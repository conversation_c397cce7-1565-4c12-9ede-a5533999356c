package miniapp.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class JsonUtil {

    private final ObjectMapper objectMapper;

    public <T> T convertValue(Object value, Class<T> targetType) {
        if (value == null) {
            return null;
        }

        try {
            return objectMapper.convertValue(value, targetType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public <T> T convertValue(Object value, TypeReference<T> targetType) {
        if (value == null) {
            return null;
        }

        try {
            return objectMapper.convertValue(value, targetType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public String toJson(Object value) {
        if (value == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public <T> T fromJson(String json, Class<T> targetType) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.readValue(json, targetType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public <T> T fromJson(String json, TypeReference<T> targetType) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.readValue(json, targetType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public <T> JsonNode toTree(T value) {
        if (value == null) {
            return null;
        }

        try {
            return objectMapper.valueToTree(value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public <T> T treeToValue(JsonNode node, Class<T> targetType) {
        if (node == null) {
            return null;
        }

        try {
            return objectMapper.treeToValue(node, targetType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public <T> T treeToValue(JsonNode node, TypeReference<T> targetType) {
        if (node == null) {
            return null;
        }

        try {
            return objectMapper.treeToValue(node, targetType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}
