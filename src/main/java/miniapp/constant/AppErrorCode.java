package miniapp.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AppErrorCode {

    //--
    SUCCESS(0, "Success"),
    ERROR(-1, "Error"),
    NOT_FOUND(404, "Resource not found"),
    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),
    BAD_REQUEST(400, "Bad Request"),
    UNAUTHORIZED(401, "Unauthorized"),
    FORBIDDEN(403, "Forbidden"),
    METHOD_NOT_ALLOWED(405, "Method Not Allowed"),
    TOO_MANY_REQUESTS(429, "Too Many Requests"),

    // Common errors
    RESOURCE_NOT_FOUND(1000001, "%s not found with %s=%s"),
    RESOURCE_ALREADY_EXISTS(1000002, "%s already exists with %s=%s"),
    VALIDATION_ERROR(1000003, "Validation failed"),
    CONSTRAINT_VIOLATION(1000004, "Constraint violation"),
    INVALID_REQUEST_BODY(1000005, "Invalid request body"),
    INVALID_FIELD_VALUE(1000006, "Invalid value for field: %s"),
    MISSING_REQUIRED_FIELD(1000007, "Missing required field: %s"),

    // MiniApp Entity Service (1001xxx)
    MINIAPP_INVALID_STATUS(1001003, "Invalid MiniApp status"),

    // MiniApp Management Service (1002xxx)
    MINIAPP_KMS_ENCRYPTION_FAILED(1002001, "Failed to encrypt data using KMS"),
    MINIAPP_KMS_DECRYPTION_FAILED(1002002, "Failed to decrypt data using KMS"),
    MINIAPP_KEYPAIR_GENERATION_FAILED(1002003, "Failed to generate keypair"),
    MINIAPP_SECRET_GENERATION_FAILED(1002004, "Failed to generate secret"),
    MINIAPP_WEBHOOK_NOT_ACTIVE(1002005, "Webhook is not active"),
    MINIAPP_CANNOT_REGENERATE_SECRET_ACTIVE(1002006, "Cannot regenerate secret for active MiniApp"),


    // MiniApp Bundle Service (1003)
    // MiniApp User Service (1004)
    ;

    private final int code;
    private final String message;

}
