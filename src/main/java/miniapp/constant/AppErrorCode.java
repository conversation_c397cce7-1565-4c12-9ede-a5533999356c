package miniapp.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AppErrorCode {

    //--
    SUCCESS(0, "Thành công"),
    ERROR(-1, "<PERSON>ó lỗi xảy ra"),

    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),
    NOT_FOUND(404, "%s Not Found"),
    BAD_REQUEST(400, "Bad Request"),

    // Code = xxxxyyy : xxxx = service code, yyy = error code start from 001
    // MiniApp All Entity Service (1000)
    // MiniApp Management Service (1001)
    // MiniApp Bundle Service (1002)
    // MiniApp User Service (1003)

    // MiniApp Entity Service (1000xxx)
    MINIAPP_NOT_FOUND(1000001, "MiniApp not found"),
    MINIAPP_CODE_DUPLICATE(1000002, "MiniApp code already exists"),
    MINIAPP_INVALID_STATUS(1000003, "Invalid MiniApp status"),
    MINIAPP_INVALID_TYPE(1000004, "Invalid MiniApp type"),

    // MiniApp Management Service (1001xxx)
    MINIAPP_KMS_ENCRYPTION_FAILED(1001001, "Failed to encrypt data using KMS"),
    MINIAPP_KMS_DECRYPTION_FAILED(1001002, "Failed to decrypt data using KMS"),
    MINIAPP_KEYPAIR_GENERATION_FAILED(1001003, "Failed to generate keypair"),
    MINIAPP_SECRET_GENERATION_FAILED(1001004, "Failed to generate secret"),
    MINIAPP_WEBHOOK_NOT_ACTIVE(1001005, "Webhook is not active"),
    MINIAPP_CANNOT_REGENERATE_SECRET_ACTIVE(1001006, "Cannot regenerate secret for active MiniApp"),

    ;

    private final int code;
    private final String message;

}
