package miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MiniAppType {
    WEB("web"),
    NATIVE("native"),
    FLUTTER("flutter")
    ;

    private final String code;

    @JsonCreator
    public static MiniAppType fromCode(String value) {
        for (MiniAppType type : MiniAppType.values()) {
            if (type.code.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid MiniAppType value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
