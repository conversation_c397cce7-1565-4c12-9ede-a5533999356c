package miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SubmissionType {
    MANIFEST("manifest"),
    BUNDLE("bundle")
    ;

    private final String code;

    @JsonCreator
    public static SubmissionType fromCode(String value) {
        for (SubmissionType type : SubmissionType.values()) {
            if (type.code.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid SubmissionType value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
