package miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MiniAppStatus {
    REGISTERED("REGISTERED"),
    ACTIVE("ACTIVE"),
    INACTIVE("INACTIVE");

    private final String code;

    @JsonCreator
    public static MiniAppStatus fromCode(String value) {
        for (MiniAppStatus status : MiniAppStatus.values()) {
            if (status.code.equalsIgnoreCase(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid MiniAppStatus value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
