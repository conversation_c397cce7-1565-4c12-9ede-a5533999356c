package miniapp.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum Environment {
    DEVELOPMENT("dev"),
    TESTING("test"),
    PRODUCTION("prod")
    ;

    private final String code;

    @JsonCreator
    public static Environment fromCode(String value) {
        for (Environment type : Environment.values()) {
            if (type.code.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid Environment value: " + value);
    }

    @JsonValue
    public String toCode() {
        return code;
    }
}
