package miniapp.model.base;

import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import miniapp.annotation.UUIDv7Generated;

@MappedSuperclass
@Getter
@Setter
@ToString
@FieldNameConstants
public abstract class BaseEntityUUID extends BaseEntity {

    @Id
    @UUIDv7Generated
    private String id;

}
