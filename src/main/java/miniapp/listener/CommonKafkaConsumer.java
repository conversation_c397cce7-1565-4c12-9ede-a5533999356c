package miniapp.listener;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CommonKafkaConsumer {

    @KafkaListener(
            topicPattern = ".*",
            groupId = "${kafka.properties.group.id}",
            containerFactory = "autoCommitKafkaListenerContainerFactory",
            autoStartup = "true"
    )
    public void testListenerAutoCommit(String message) {
        // Process the incoming message
        log.info(message);
    }

    @KafkaListener(
            topicPattern = ".*",
            groupId = "${kafka.properties.group.id}_manual",
            containerFactory = "manualCommitKafkaListenerContainerFactory",
            autoStartup = "true"
    )
    public void testListenerManuallyCommit(ConsumerRecord<String, String> record, Acknowledgment ack) {
        // Process the incoming message
        log.info(record.value());
        ack.acknowledge();
        log.info("Message acknowledged, topic: {}, offset: {}, partition: {}", record.topic(), record.offset(), record.partition());
    }

}
