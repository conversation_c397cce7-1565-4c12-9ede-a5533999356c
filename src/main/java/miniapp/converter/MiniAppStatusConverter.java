package miniapp.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import miniapp.constant.MiniAppStatus;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.util.ObjectUtils;

@Converter
public class MiniAppStatusConverter implements AttributeConverter<MiniAppStatus, String>, BeanPostProcessor {
    public String convertToDatabaseColumn(MiniAppStatus attribute) {
        return ObjectUtils.isEmpty(attribute) ? null : attribute.toCode();
    }

    public MiniAppStatus convertToEntityAttribute(String dbData) {
        return MiniAppStatus.fromCode(dbData);
    }
}
