//package miniapp.configuration;
//
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.config.Config;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class KvRocksConfig {
//    @Value("${kvrocks.config.address}")
//    private String redissonAddress;
//
//    @Value("${kvrocks.config.password:}")
//    private String redissonPassword;
//
//    @Value("${kvrocks.config.connection.max:64}")
//    private int connectionMaxPoolSize;
//
//    @Value("${kvrocks.config.connection.min:24}")
//    private int connectionCorePoolSize;
//
//    @Value("${kvrocks.config.connection.timeout:3000}")
//    private int connectionTimeoutMs;
//
//    @Value("${kvrocks.config.response.timeout:3000}")
//    private int responseTimeoutMs;
//
//    @Value("${kvrocks.config.database:0}")
//    private int database;
//
//    @Value("${kvrocks.config.connection.idle.time:0}")
//    private int connectionIdleTimeMs;
//
//    @Bean
//    public RedissonClient kvrocksClient() {
//        Config config = new Config();
//        config.useSingleServer()
//                .setConnectionPoolSize(connectionMaxPoolSize)
//                .setConnectionMinimumIdleSize(connectionCorePoolSize)
//                .setIdleConnectionTimeout(connectionIdleTimeMs)
//                .setConnectTimeout(connectionTimeoutMs)
//                .setTimeout(responseTimeoutMs)
//                .setDatabase(database)
//                .setKeepAlive(true)
//                .setAddress(redissonAddress)
//                .setPassword(redissonPassword.isEmpty() ? null : redissonPassword);
//        return Redisson.create(config);
//    }
//}
