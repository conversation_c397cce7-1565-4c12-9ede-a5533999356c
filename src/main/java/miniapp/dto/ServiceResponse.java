package miniapp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import miniapp.constant.AppErrorCode;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceResponse<T> {

    private int code;

    @Builder.Default
    private List<String> messages = new ArrayList<>();

    private String message;

    private T data;

    public ServiceResponse<T> message(String msg) {
        messages.add(msg);
        message = messages.getFirst();
        return this;
    }

    public ServiceResponse<T> messages(List<String> msg) {
        messages.addAll(msg);
        message = messages.getFirst();
        return this;
    }

    public static <T> ServiceResponse<T> success(T data) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(AppErrorCode.SUCCESS.getCode()).data(data).build();
        res.message(AppErrorCode.SUCCESS.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> success(T data, String message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(AppErrorCode.SUCCESS.getCode()).data(data).build();
        res.message(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(T data, int errorCode, String message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).data(data).build();
        res.message(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(T data, AppErrorCode errorCodeEnum) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).data(data).build();
        res.message(errorCodeEnum.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> error(int errorCode, List<String> message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).build();
        res.messages(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(int errorCode, String message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).build();
        res.message(message);
        return res;
    }

    public static <T> ServiceResponse<T> error(AppErrorCode errorCodeEnum) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).build();
        res.message(errorCodeEnum.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> error(AppErrorCode errorCodeEnum, Object... args) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).build();
        res.message(String.format(errorCodeEnum.getMessage(), args));
        return res;
    }

    public static <T> ServiceResponse<T> error(int errorCode, String message, Object... args) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCode).build();
        res.message(String.format(message, args));
        return res;
    }

    public static <T> ServiceResponse<T> error(List<String> data, int errorCode) {
        return ServiceResponse.<T>builder().code(errorCode).build().messages(data);
    }

    public static <T> ServiceResponse<T> error(T data, AppErrorCode errorCodeEnum, Object... args) {
        ServiceResponse<T> res = ServiceResponse.<T>builder().code(errorCodeEnum.getCode()).data(data).build();
        res.message(String.format(errorCodeEnum.getMessage(), args));
        return res;
    }
}
