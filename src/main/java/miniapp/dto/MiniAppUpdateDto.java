package miniapp.dto;

import jakarta.validation.constraints.Size;
import lombok.experimental.FieldNameConstants;
import miniapp.annotation.*;
import miniapp.constant.ImageType;
import miniapp.constant.MiniAppPermission;
import miniapp.constant.MiniAppType;

import java.util.Map;
import java.util.Set;

/**
 * DTO for updating an existing MiniApp
 * Does not allow updating code, secret, signingKey, or webhookSecret
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@FieldNameConstants
public record MiniAppUpdateDto(

        @Size(max = 100, message = "Name must not exceed 100 characters")
        String name,

        Long categoryId,

        Long orgCode,

        MiniAppType type,

        @Size(min = 1, message = "Display names must not be empty")
        Map<String, String> displayNames,

        @Size(min = 1, message = "Descriptions must not be empty")
        @ValidateTextLocaleMap(minLength = 10, maxLength = 500)
        Map<String, String> descriptions,

        @Size(min = 1, message = "Logos must not be empty")
        @ValidateImageLocaleMap(supportedTypes = {ImageType.PNG, ImageType.JPG, ImageType.JPEG}, message = "Invalid logo URL in locale map")
        Map<String, String> logos,

        @ValidateImageLocaleMap(supportedTypes = {ImageType.PNG, ImageType.JPG, ImageType.JPEG}, message = "Invalid banner URL in locale map")
        Map<String, String> banners,

        @Size(max = 10, message = "Showcase must not exceed 10 items")
        @ValidateImageUrls(supportedTypes = {ImageType.PNG, ImageType.JPG, ImageType.JPEG}, message = "Invalid showcase image URL")
        Set<String> showcase,

        Set<MiniAppUserScope> scopes,

        Set<MiniAppPermission> permissions,

        @Size(max = 20, message = "Whitelist IPs must not exceed 20 items")
        @ValidateIPs(message = "Whitelist IPs must be valid IPv4, IPv6, or CIDR notation")
        Set<String> whitelistIps,

        @Size(max = 20, message = "Whitelist domains must not exceed 20 items")
        @ValidateDomains(message = "Whitelist domains must be valid domain names")
        Set<String> whitelistDomains
) {
}

