package miniapp.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import miniapp.dto.ServiceResponse;

import java.io.Serial;

@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessLogicException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;
    private ServiceResponse<?> payload;

    public BusinessLogicException(ServiceResponse<?> payload) {
        this.payload = payload;
    }
}

