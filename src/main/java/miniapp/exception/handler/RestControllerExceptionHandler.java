package miniapp.exception.handler;

import lombok.extern.slf4j.Slf4j;
import miniapp.constant.AppErrorCode;
import miniapp.dto.ServiceResponse;
import miniapp.exception.BusinessLogicException;
import miniapp.exception.IgnoreProcessingException;
import miniapp.exception.ResourceNotFoundException;
import org.apache.coyote.BadRequestException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import org.springframework.web.servlet.resource.NoResourceFoundException;

@ControllerAdvice
@Slf4j
public class RestControllerExceptionHandler extends ResponseEntityExceptionHandler {

    @Override
    @ExceptionHandler(ResourceNotFoundException.class)
    protected ResponseEntity<Object> handleNoResourceFoundException(NoResourceFoundException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String notFoundMessage = String.format("No handler found for %s %s", ex.getHttpMethod(), ex.getResourcePath());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.NOT_FOUND.getCode())
                .message(notFoundMessage)
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String notFoundMessage = String.format("No handler found for %s %s", ex.getHttpMethod(), ex.getRequestURL());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.NOT_FOUND.getCode())
                .message(notFoundMessage)
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }


    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("handleHttpMessageNotReadable: {}\theader={}\tstatus={}\tWebRequest={}", ex.getMessage(), headers, status, request);

        return new ResponseEntity<>(
                ServiceResponse.error(AppErrorCode.BAD_REQUEST),
                HttpStatus.BAD_REQUEST
        );
    }

    @ExceptionHandler({BusinessLogicException.class, BadRequestException.class})
    public ResponseEntity<ServiceResponse<?>> handleBusinessLogicException(BusinessLogicException ex) {
        ServiceResponse<?> response = ex.getPayload();
        if (response != null) {
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        } else {
            return handleAllExceptions(ex);
        }
    }

    @ExceptionHandler({IgnoreProcessingException.class})
    public ResponseEntity<?> handleIgnoreProcessingException(IgnoreProcessingException e) {
        return new ResponseEntity<>(e.getPayload(), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ServiceResponse<?>> handleAllExceptions(Exception ex) {
        ServiceResponse<Object> response = ServiceResponse.builder()
                .data(null)
                .code(AppErrorCode.INTERNAL_SERVER_ERROR.getCode())
                .message(ex.getMessage() != null ? ex.getMessage() : "Internal Server Error")
                .build();
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
}

