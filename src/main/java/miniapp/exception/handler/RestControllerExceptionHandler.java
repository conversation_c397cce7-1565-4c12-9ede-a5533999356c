package miniapp.exception.handler;

import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import miniapp.constant.AppErrorCode;
import miniapp.dto.ServiceResponse;
import miniapp.exception.BusinessLogicException;
import miniapp.exception.IgnoreProcessingException;
import miniapp.exception.ResourceExistedException;
import miniapp.exception.ResourceNotFoundException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Global exception handler for REST controllers.
 * Handles all exceptions and converts them to consistent ServiceResponse format.
 *
 * <AUTHOR>
 * @since 2025-10-02
 */
@ControllerAdvice
@Slf4j
public class RestControllerExceptionHandler extends ResponseEntityExceptionHandler {

    /**
     * Handle BusinessLogicException - thrown for business rule violations.
     * Returns BAD_REQUEST status with error details from the exception payload.
     */
    @ExceptionHandler(BusinessLogicException.class)
    public ResponseEntity<ServiceResponse<?>> handleBusinessLogicException(BusinessLogicException ex) {
        ServiceResponse<?> response = ex.getPayload();
        if (response != null) {
            log.warn("Business logic exception: code={}, message={}", response.getCode(), response.getMessage());
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        } else {
            log.error("Business logic exception with null payload", ex);
            return handleAllExceptions(ex);
        }
    }

    /**
     * Handle ResourceNotFoundException - thrown when a requested resource is not found.
     * Returns NOT_FOUND status with resource details.
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ServiceResponse<?>> handleResourceNotFoundException(ResourceNotFoundException ex) {
        log.warn("Resource not found: {}", ex.getMessage());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.RESOURCE_NOT_FOUND.getCode())
                .message(ex.getMessage())
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(ResourceExistedException.class)
    public ResponseEntity<ServiceResponse<?>> handleResourceExistedException(ResourceExistedException ex) {
        log.warn("Resource already exists: {}", ex.getMessage());
        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.RESOURCE_ALREADY_EXISTS.getCode())
                .message(ex.getMessage())
                .data(null)
                .build();
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle IgnoreProcessingException - thrown when processing should be ignored.
     * Returns BAD_REQUEST status with error details from the exception payload.
     */
    @ExceptionHandler(IgnoreProcessingException.class)
    public ResponseEntity<ServiceResponse<?>> handleIgnoreProcessingException(IgnoreProcessingException ex) {
        ServiceResponse<?> response = ex.getPayload();
        log.warn("Ignore processing exception: code={}, message={}",
                response != null ? response.getCode() : "N/A",
                response != null ? response.getMessage() : "N/A");
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle MethodArgumentNotValidException - thrown when @Valid validation fails on request body.
     * Returns BAD_REQUEST status with detailed validation error messages.
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        List<String> errors = new ArrayList<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.add(String.format("%s: %s", error.getField(), error.getDefaultMessage()));
        }
        for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
            errors.add(String.format("%s: %s", error.getObjectName(), error.getDefaultMessage()));
        }

        log.warn("Validation failed: {}", errors);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.VALIDATION_ERROR.getCode())
                .messages(errors)
                .message(AppErrorCode.VALIDATION_ERROR.getMessage())
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle ConstraintViolationException - thrown when @Validated validation fails on method parameters.
     * Returns BAD_REQUEST status with detailed constraint violation messages.
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ServiceResponse<?>> handleConstraintViolationException(ConstraintViolationException ex) {
        List<String> errors = ex.getConstraintViolations().stream()
                .map(violation -> String.format("%s: %s",
                        violation.getPropertyPath(),
                        violation.getMessage()))
                .collect(Collectors.toList());

        log.warn("Constraint violation: {}", errors);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.CONSTRAINT_VIOLATION.getCode())
                .messages(errors)
                .message(AppErrorCode.CONSTRAINT_VIOLATION.getMessage())
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle HttpMessageNotReadableException - thrown when request body cannot be read or parsed.
     * Returns BAD_REQUEST status indicating invalid request body format.
     */
    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        log.warn("Invalid request body: {}", ex.getMessage());

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.INVALID_REQUEST_BODY.getCode())
                .message(AppErrorCode.INVALID_REQUEST_BODY.getMessage())
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handle NoResourceFoundException - thrown when Spring cannot find a resource (e.g., static files).
     * Returns NOT_FOUND status with resource path details.
     */
    @Override
    protected ResponseEntity<Object> handleNoResourceFoundException(
            NoResourceFoundException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String notFoundMessage = String.format("No resource found for %s %s", ex.getHttpMethod(), ex.getResourcePath());
        log.warn(notFoundMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.NOT_FOUND.getCode())
                .message(notFoundMessage)
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    /**
     * Handle NoHandlerFoundException - thrown when Spring cannot find a handler for the request.
     * Returns NOT_FOUND status with request URL details.
     */
    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(
            NoHandlerFoundException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String notFoundMessage = String.format("No handler found for %s %s", ex.getHttpMethod(), ex.getRequestURL());
        log.warn(notFoundMessage);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.NOT_FOUND.getCode())
                .message(notFoundMessage)
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    /**
     * Handle all other uncaught exceptions.
     * Returns INTERNAL_SERVER_ERROR status with generic error message.
     * Logs the full exception for debugging purposes.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ServiceResponse<?>> handleAllExceptions(Exception ex) {
        log.error("Unhandled exception occurred", ex);

        ServiceResponse<Object> response = ServiceResponse.builder()
                .code(AppErrorCode.INTERNAL_SERVER_ERROR.getCode())
                .message(AppErrorCode.INTERNAL_SERVER_ERROR.getMessage())
                .data(null)
                .build();

        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

