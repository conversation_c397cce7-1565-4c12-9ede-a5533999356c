package miniapp.service;

import miniapp.dto.MiniAppDto;
import miniapp.dto.MiniAppFilter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.Set;

/**
 * Service interface for MiniApp entity operations
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
public interface MiniAppService {

    /**
     * Create a new MiniApp
     *
     * @param dto the MiniApp data
     * @return created MiniAppDto
     */
    MiniAppDto create(MiniAppDto dto);

    /**
     * Get MiniApp by ID (cached)
     *
     * @param id the MiniApp ID
     * @return MiniAppDto
     */
    MiniAppDto getById(String id);

    /**
     * Get MiniApp by code (cached)
     *
     * @param code the unique code
     * @return MiniAppDto
     */
    MiniAppDto getByCode(String code);

    /**
     * Get MiniApp by ID (cached)
     *
     * @param id the MiniApp ID
     * @return Optional of MiniAppDto
     */
    Optional<MiniAppDto> optById(String id);

    /**
     * Get MiniApp by code (cached)
     *
     * @param code the unique code
     * @return Optional of MiniAppDto
     */
    Optional<MiniAppDto> optByCode(String code);

    /**
     * Update MiniApp
     *
     * @param id  the MiniApp ID
     * @param dto the updated data
     * @return updated MiniAppDto
     */
    MiniAppDto update(String id, MiniAppDto dto);

    /**
     * Partial update MiniApp
     * Only non-null fields in DTO will be updated
     *
     * @param id  the MiniApp ID
     * @param dto the partial update data
     * @return updated MiniAppDto
     */
    MiniAppDto updatePartial(String id, MiniAppDto dto);

    /**
     * Delete MiniApp by ID
     *
     * @param id the MiniApp ID
     */
    void delete(String id);

    /**
     * Search MiniApps with filter and pagination
     *
     * @param filter   the filter criteria
     * @param pageable pagination information
     * @return Page of MiniAppDto
     */
    Page<MiniAppDto> search(MiniAppFilter filter, Pageable pageable);

    /**
     * Check if MiniApp exists by code
     *
     * @param code the unique code
     * @return true if exists
     */
    boolean existsByCode(String code);


    /**
     * Check if MiniApp is active
     *
     * @param id the MiniApp ID
     * @return true if active
     */
    boolean isActive(String id);

    /**
     * Check if MiniApp is registered
     *
     * @param id the MiniApp ID
     * @return true if registered
     */
    boolean isRegistered(String id);

    /**
     * Check if MiniApp is inactive
     *
     * @param id the MiniApp ID
     * @return true if inactive
     */
    boolean isInactive(String id);


    /**
     * Invalidate cache for given IDs and codes
     *
     * @param ids   the MiniApp IDs
     * @param codes the MiniApp codes
     */
    void invalidateCache(Set<String> ids, Set<String> codes);
}

