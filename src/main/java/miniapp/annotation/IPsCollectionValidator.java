package miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import miniapp.util.CStringUtils;

import java.util.Collection;

/**
 * Validator for {@link ValidateIPs} annotation for collection of IP addresses.
 * Validates each IP address in the collection (IPv4, IPv6, and CIDR notation).
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class IPsCollectionValidator implements ConstraintValidator<ValidateIPs, Collection<String>> {

    @Override
    public boolean isValid(Collection<String> value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Validate each IP in the collection
        for (String ip : value) {
            // Skip null values
            if (ip == null) {
                continue;
            }

            String trimmedIp = ip.trim();
            if (trimmedIp.isEmpty()) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        "Empty IP address found in collection"
                ).addConstraintViolation();
                return false;
            }

            if (!CStringUtils.isValidIpAddress(trimmedIp)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        String.format("Invalid IP address or CIDR notation: %s", ip)
                ).addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}

