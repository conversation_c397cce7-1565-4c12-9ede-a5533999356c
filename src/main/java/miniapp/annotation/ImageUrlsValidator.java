package miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import miniapp.constant.ImageType;
import miniapp.util.CStringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Validator for {@link ValidateImageUrls} annotation for single image URL (String).
 * Validates that the URL is a valid HTTP/HTTPS URL with a supported image file extension.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class ImageUrlsValidator implements ConstraintValidator<ValidateImageUrls, String> {

    private Set<ImageType> supportedTypes;

    @Override
    public void initialize(ValidateImageUrls constraintAnnotation) {
        // Convert supported types to lowercase for case-insensitive comparison
        this.supportedTypes = Arrays.stream(constraintAnnotation.supportedTypes())
                .collect(Collectors.toSet());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Trim whitespace
        String trimmedValue = value.trim();
        if (trimmedValue.isEmpty()) {
            return false;
        }

        return CStringUtils.isValidImageUrl(trimmedValue, supportedTypes);
    }
}

