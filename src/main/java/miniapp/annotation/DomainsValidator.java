package miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import miniapp.util.CStringUtils;

/**
 * Validator for {@link ValidateDomains} annotation for single domain name (String).
 * Validates domain names according to RFC 1035/1123.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class DomainsValidator implements ConstraintValidator<ValidateDomains, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Trim whitespace
        String trimmedValue = value.trim();
        if (trimmedValue.isEmpty()) {
            return false;
        }

        return CStringUtils.isValidDomain(trimmedValue);
    }
}

