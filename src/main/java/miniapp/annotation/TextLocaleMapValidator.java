package miniapp.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Map;

/**
 * Validator for {@link ValidateTextLocaleMap} annotation.
 * Validates that all values in a Map&lt;String, String&gt; have text length within the specified range.
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
public class TextLocaleMapValidator implements ConstraintValidator<ValidateTextLocaleMap, Map<String, String>> {

    private int minLength;
    private int maxLength;

    @Override
    public void initialize(ValidateTextLocaleMap constraintAnnotation) {
        this.minLength = constraintAnnotation.minLength();
        this.maxLength = constraintAnnotation.maxLength();
    }

    @Override
    public boolean isValid(Map<String, String> value, ConstraintValidatorContext context) {
        // Null values are considered valid (use @NotNull separately if needed)
        if (value == null) {
            return true;
        }

        // Validate each text value in the map
        for (Map.Entry<String, String> entry : value.entrySet()) {
            String text = entry.getValue();
            
            // Skip null values
            if (text == null) {
                continue;
            }

            int length = text.length();

            // Check minimum length
            if (length < minLength) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        String.format("Text for locale '%s' is too short: %d characters (minimum: %d)",
                                entry.getKey(), length, minLength)
                ).addConstraintViolation();
                return false;
            }

            // Check maximum length
            if (length > maxLength) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                        String.format("Text for locale '%s' is too long: %d characters (maximum: %d)",
                                entry.getKey(), length, maxLength)
                ).addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}

