#!/usr/bin/env bash
set -euo pipefail

BUCKET_NAME="${BUCKET_NAME:-my-bucket}"
KMS_ALIAS="${KMS_ALIAS:-alias/dev-local}"

echo "[init] Checking/creating S3 bucket: ${BUCKET_NAME}"
if awslocal s3api head-bucket --bucket "${BUCKET_NAME}" 2>/dev/null; then
  echo "[init] S3 bucket exists: ${BUCKET_NAME}"
else
  awslocal s3 mb "s3://${BUCKET_NAME}"
  # optional: set public-block off for local testing
  awslocal s3api put-public-access-block \
    --bucket "${BUCKET_NAME}" \
    --public-access-block-configuration BlockPublicAcls=false,IgnorePublicAcls=false,BlockPublicPolicy=false,RestrictPublicBuckets=false
  echo "[init] Created bucket: ${BUCKET_NAME}"
fi

echo "[init] Checking/creating KMS key alias: ${KMS_ALIAS}"
ALIAS_EXISTS=$(awslocal kms list-aliases --query "Aliases[?AliasName=='${KMS_ALIAS}'] | length(@)")
if [[ "${ALIAS_EXISTS}" -gt 0 ]]; then
  echo "[init] KMS alias exists: ${KMS_ALIAS}"
else
  KEY_ID=$(awslocal kms create-key --description "dev-local key" --origin INTERNAL --query KeyMetadata.KeyId --output text)
  awslocal kms create-alias --alias-name "${KMS_ALIAS}" --target-key-id "${KEY_ID}"
  echo "[init] Created KMS key ${KEY_ID} and alias ${KMS_ALIAS}"
fi
