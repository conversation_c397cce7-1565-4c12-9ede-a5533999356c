package vfs.miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for IPsCollectionValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class IPsCollectionValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    private IPsCollectionValidator validator;

    @BeforeEach
    void setUp() {
        validator = new IPsCollectionValidator();

        // Setup mock behavior for constraint violation
        lenient().when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);
    }

    @Test
    void isValid_nullCollection_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_emptyCollection_returnsTrue() {
        assertTrue(validator.isValid(new ArrayList<>(), context));
        assertTrue(validator.isValid(new HashSet<>(), context));
    }

    @Test
    void isValid_validIPv4List_returnsTrue() {
        List<String> validIPs = Arrays.asList(
                "***********",
                "********",
                "**********"
        );
        assertTrue(validator.isValid(validIPs, context));
    }

    @Test
    void isValid_validIPv4CIDRList_returnsTrue() {
        List<String> validIPs = Arrays.asList(
                "***********/24",
                "10.0.0.0/8",
                "**********/16"
        );
        assertTrue(validator.isValid(validIPs, context));
    }

    @Test
    void isValid_validIPv6List_returnsTrue() {
        List<String> validIPs = Arrays.asList(
                "2001:db8::1",
                "::1",
                "fe80::1"
        );
        assertTrue(validator.isValid(validIPs, context));
    }

    @Test
    void isValid_validIPv6CIDRList_returnsTrue() {
        List<String> validIPs = Arrays.asList(
                "2001:db8::/32",
                "fe80::/10",
                "::1/128"
        );
        assertTrue(validator.isValid(validIPs, context));
    }

    @Test
    void isValid_mixedIPv4AndIPv6_returnsTrue() {
        List<String> validIPs = Arrays.asList(
                "***********",
                "2001:db8::1",
                "10.0.0.0/8",
                "fe80::/10"
        );
        assertTrue(validator.isValid(validIPs, context));
    }

    @Test
    void isValid_validSet_returnsTrue() {
        Set<String> validIPs = new HashSet<>(Arrays.asList(
                "***********",
                "********",
                "2001:db8::1"
        ));
        assertTrue(validator.isValid(validIPs, context));
    }

    @Test
    void isValid_collectionWithNullValue_returnsTrue() {
        List<String> ipsWithNull = new ArrayList<>();
        ipsWithNull.add("***********");
        ipsWithNull.add(null);
        ipsWithNull.add("********");

        assertTrue(validator.isValid(ipsWithNull, context));
    }

    @Test
    void isValid_invalidIPInCollection_returnsFalse() {
        List<String> invalidIPs = Arrays.asList(
                "***********",
                "invalid-ip",
                "********"
        );

        assertFalse(validator.isValid(invalidIPs, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Invalid IP address"));
    }

    @Test
    void isValid_emptyStringInCollection_returnsFalse() {
        List<String> invalidIPs = Arrays.asList(
                "***********",
                "",
                "********"
        );

        assertFalse(validator.isValid(invalidIPs, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Empty IP address"));
    }

    @Test
    void isValid_whitespaceOnlyInCollection_returnsFalse() {
        List<String> invalidIPs = Arrays.asList(
                "***********",
                "   ",
                "********"
        );

        assertFalse(validator.isValid(invalidIPs, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_invalidIPv4InCollection_returnsFalse() {
        List<String> invalidIPs = Arrays.asList(
                "***********",
                "256.1.1.1",
                "********"
        );

        assertFalse(validator.isValid(invalidIPs, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_invalidCIDRInCollection_returnsFalse() {
        List<String> invalidIPs = Arrays.asList(
                "***********/24",
                "10.0.0.0/33",
                "**********/16"
        );

        assertFalse(validator.isValid(invalidIPs, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_whitespaceAroundValidIPs_returnsTrue() {
        List<String> validIPs = Arrays.asList(
                "  ***********  ",
                "  ********  ",
                "  2001:db8::1  "
        );
        assertTrue(validator.isValid(validIPs, context));
    }
}

