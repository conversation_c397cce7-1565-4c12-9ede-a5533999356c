package vfs.miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for TextLocaleMapValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class TextLocaleMapValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    private TextLocaleMapValidator validator;

    @BeforeEach
    void setUp() {
        validator = new TextLocaleMapValidator();

        // Setup mock behavior for constraint violation (lenient to avoid unnecessary stubbing failures)
        lenient().when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);
    }

    private ValidateTextLocaleMap createAnnotation(int minLength, int maxLength) {
        return new ValidateTextLocaleMap() {
            @Override
            public String message() {
                return "Invalid text length in locale map";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public Class<? extends jakarta.validation.Payload>[] payload() {
                return new Class[0];
            }

            @Override
            public int minLength() {
                return minLength;
            }

            @Override
            public int maxLength() {
                return maxLength;
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return ValidateTextLocaleMap.class;
            }
        };
    }

    @Test
    void isValid_nullMap_returnsTrue() {
        validator.initialize(createAnnotation(0, Integer.MAX_VALUE));
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_emptyMap_returnsTrue() {
        validator.initialize(createAnnotation(0, Integer.MAX_VALUE));
        Map<String, String> emptyMap = new HashMap<>();
        assertTrue(validator.isValid(emptyMap, context));
    }

    @Test
    void isValid_validTextWithinRange_returnsTrue() {
        validator.initialize(createAnnotation(5, 50));
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "Hello World");
        validMap.put("vi", "Xin chào");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_textTooShort_returnsFalse() {
        validator.initialize(createAnnotation(10, 100));
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "Short");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("too short"));
    }

    @Test
    void isValid_textTooLong_returnsFalse() {
        validator.initialize(createAnnotation(0, 10));
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "This text is way too long for the maximum length");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("too long"));
    }

    @Test
    void isValid_textExactlyMinLength_returnsTrue() {
        validator.initialize(createAnnotation(5, 100));
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "Hello");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_textExactlyMaxLength_returnsTrue() {
        validator.initialize(createAnnotation(0, 5));
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "Hello");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_mapWithNullValue_returnsTrue() {
        validator.initialize(createAnnotation(5, 100));
        Map<String, String> mapWithNull = new HashMap<>();
        mapWithNull.put("en", "Valid text");
        mapWithNull.put("vi", null);

        assertTrue(validator.isValid(mapWithNull, context));
    }

    @Test
    void isValid_emptyStringWithMinZero_returnsTrue() {
        validator.initialize(createAnnotation(0, 100));
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_emptyStringWithMinGreaterThanZero_returnsFalse() {
        validator.initialize(createAnnotation(1, 100));
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_defaultMinMaxValues_returnsTrue() {
        validator.initialize(createAnnotation(0, Integer.MAX_VALUE));
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "Any length text should be valid");
        validMap.put("vi", "");
        validMap.put("fr", "A".repeat(1000));

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_multipleLocalesOneInvalid_returnsFalse() {
        validator.initialize(createAnnotation(5, 20));
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "Valid text");
        invalidMap.put("vi", "Too");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_unicodeCharacters_returnsTrue() {
        validator.initialize(createAnnotation(5, 50));
        Map<String, String> validMap = new HashMap<>();
        validMap.put("vi", "Xin chào thế giới");
        validMap.put("ja", "こんにちは世界");
        validMap.put("ar", "مرحبا بالعالم");

        assertTrue(validator.isValid(validMap, context));
    }
}

