package vfs.miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Unit tests for IPsValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class IPsValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    private IPsValidator validator;

    @BeforeEach
    void setUp() {
        validator = new IPsValidator();
    }

    @Test
    void isValid_nullValue_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_validIPv4_returnsTrue() {
        assertTrue(validator.isValid("***********", context));
        assertTrue(validator.isValid("********", context));
        assertTrue(validator.isValid("**********", context));
        assertTrue(validator.isValid("***************", context));
        assertTrue(validator.isValid("0.0.0.0", context));
    }

    @Test
    void isValid_validIPv4CIDR_returnsTrue() {
        assertTrue(validator.isValid("***********/24", context));
        assertTrue(validator.isValid("10.0.0.0/8", context));
        assertTrue(validator.isValid("**********/16", context));
        assertTrue(validator.isValid("***********/32", context));
        assertTrue(validator.isValid("0.0.0.0/0", context));
    }

    @Test
    void isValid_validIPv6_returnsTrue() {
        assertTrue(validator.isValid("2001:0db8:85a3:0000:0000:8a2e:0370:7334", context));
        assertTrue(validator.isValid("2001:db8:85a3::8a2e:370:7334", context));
        assertTrue(validator.isValid("::1", context));
        assertTrue(validator.isValid("::", context));
        assertTrue(validator.isValid("fe80::1", context));
        assertTrue(validator.isValid("2001:db8::1", context));
    }

    @Test
    void isValid_validIPv6CIDR_returnsTrue() {
        assertTrue(validator.isValid("2001:db8::/32", context));
        assertTrue(validator.isValid("fe80::/10", context));
        assertTrue(validator.isValid("::1/128", context));
        assertTrue(validator.isValid("2001:db8:85a3::8a2e:370:7334/64", context));
    }

    @Test
    void isValid_invalidIPv4_returnsFalse() {
        assertFalse(validator.isValid("256.1.1.1", context));
        assertFalse(validator.isValid("192.168.1", context));
        assertFalse(validator.isValid("***********.1", context));
        assertFalse(validator.isValid("192.168.-1.1", context));
        assertFalse(validator.isValid("abc.def.ghi.jkl", context));
    }

    @Test
    void isValid_invalidIPv4CIDR_returnsFalse() {
        assertFalse(validator.isValid("***********/33", context));
        assertFalse(validator.isValid("***********/-1", context));
        assertFalse(validator.isValid("***********/", context));
        assertFalse(validator.isValid("256.1.1.1/24", context));
    }

    @Test
    void isValid_invalidIPv6_returnsFalse() {
        assertFalse(validator.isValid("gggg::1", context));
        assertFalse(validator.isValid(":::", context));
        assertFalse(validator.isValid("2001:db8::g", context));
    }

    @Test
    void isValid_invalidIPv6CIDR_returnsFalse() {
        assertFalse(validator.isValid("2001:db8::/129", context));
        assertFalse(validator.isValid("2001:db8::/-1", context));
        assertFalse(validator.isValid("::1/", context));
    }

    @Test
    void isValid_emptyString_returnsFalse() {
        assertFalse(validator.isValid("", context));
        assertFalse(validator.isValid("   ", context));
    }

    @Test
    void isValid_whitespaceAroundValidIP_returnsTrue() {
        assertTrue(validator.isValid("  ***********  ", context));
        assertTrue(validator.isValid("  2001:db8::1  ", context));
    }

    @Test
    void isValid_invalidFormat_returnsFalse() {
        assertFalse(validator.isValid("not-an-ip", context));
        assertFalse(validator.isValid("***********.1.1", context));
        assertFalse(validator.isValid("192.168", context));
    }

    @Test
    void isValid_localhostIPv4_returnsTrue() {
        assertTrue(validator.isValid("127.0.0.1", context));
        assertTrue(validator.isValid("127.0.0.1/8", context));
    }

    @Test
    void isValid_localhostIPv6_returnsTrue() {
        assertTrue(validator.isValid("::1", context));
        assertTrue(validator.isValid("::1/128", context));
    }
}

