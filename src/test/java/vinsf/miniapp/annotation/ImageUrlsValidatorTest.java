package vinsf.miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import vinsf.miniapp.constant.ImageType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Unit tests for ImageUrlsValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class ImageUrlsValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    private ImageUrlsValidator validator;
    private ValidateImageUrls annotation;

    @BeforeEach
    void setUp() {
        validator = new ImageUrlsValidator();
        
        // Create mock annotation with default supported types
        annotation = new ValidateImageUrls() {
            @Override
            public String message() {
                return "Invalid image URL";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public Class<? extends jakarta.validation.Payload>[] payload() {
                return new Class[0];
            }

            @Override
            public ImageType[] supportedTypes() {
                return new ImageType[]{ImageType.PNG, ImageType.JPG, ImageType.JPEG, ImageType.GIF, ImageType.SVG, ImageType.WEBP};
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return ValidateImageUrls.class;
            }
        };

        validator.initialize(annotation);
    }

    @Test
    void isValid_nullValue_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_validImageUrls_returnsTrue() {
        assertTrue(validator.isValid("https://example.com/image.png", context));
        assertTrue(validator.isValid("https://example.com/photo.jpg", context));
        assertTrue(validator.isValid("https://example.com/picture.jpeg", context));
        assertTrue(validator.isValid("https://example.com/animation.gif", context));
        assertTrue(validator.isValid("https://example.com/icon.svg", context));
        assertTrue(validator.isValid("https://example.com/modern.webp", context));
    }

    @Test
    void isValid_httpProtocol_returnsTrue() {
        assertTrue(validator.isValid("http://example.com/image.png", context));
    }

    @Test
    void isValid_caseInsensitiveExtensions_returnsTrue() {
        assertTrue(validator.isValid("https://example.com/image.PNG", context));
        assertTrue(validator.isValid("https://example.com/image.JpG", context));
        assertTrue(validator.isValid("https://example.com/image.WebP", context));
    }

    @Test
    void isValid_urlWithQueryParameters_returnsTrue() {
        assertTrue(validator.isValid("https://example.com/image.png?size=large", context));
        assertTrue(validator.isValid("https://cdn.example.com/photo.jpg?v=123&quality=high", context));
    }

    @Test
    void isValid_urlWithPath_returnsTrue() {
        assertTrue(validator.isValid("https://example.com/images/gallery/photo.jpg", context));
        assertTrue(validator.isValid("https://cdn.example.com/static/v1/icons/logo.png", context));
    }

    @Test
    void isValid_invalidProtocol_returnsFalse() {
        assertFalse(validator.isValid("ftp://example.com/image.png", context));
        assertFalse(validator.isValid("file:///path/to/image.png", context));
    }

    @Test
    void isValid_unsupportedExtension_returnsFalse() {
        assertFalse(validator.isValid("https://example.com/document.pdf", context));
        assertFalse(validator.isValid("https://example.com/video.mp4", context));
        assertFalse(validator.isValid("https://example.com/file.txt", context));
    }

    @Test
    void isValid_noExtension_returnsFalse() {
        assertFalse(validator.isValid("https://example.com/image", context));
        assertFalse(validator.isValid("https://example.com/path/to/file", context));
    }

    @Test
    void isValid_invalidUrl_returnsFalse() {
        assertFalse(validator.isValid("not-a-url", context));
        assertFalse(validator.isValid("example.com/image.png", context));
        assertFalse(validator.isValid("//example.com/image.png", context));
    }

    @Test
    void isValid_emptyString_returnsFalse() {
        assertFalse(validator.isValid("", context));
        assertFalse(validator.isValid("   ", context));
    }

    @Test
    void isValid_whitespaceAroundValidUrl_returnsTrue() {
        assertTrue(validator.isValid("  https://example.com/image.png  ", context));
    }

    @Test
    void isValid_urlWithPort_returnsTrue() {
        assertTrue(validator.isValid("https://example.com:8080/image.png", context));
        assertTrue(validator.isValid("http://localhost:3000/static/logo.jpg", context));
    }

    @Test
    void isValid_urlWithSubdomain_returnsTrue() {
        assertTrue(validator.isValid("https://cdn.example.com/image.png", context));
        assertTrue(validator.isValid("https://static.cdn.example.com/photo.jpg", context));
    }

    @Test
    void isValid_urlWithFragment_returnsTrue() {
        assertTrue(validator.isValid("https://example.com/image.png#section", context));
    }

    @Test
    void isValid_urlWithAuthentication_returnsTrue() {
        assertTrue(validator.isValid("https://user:<EMAIL>/image.png", context));
    }

    @Test
    void isValid_urlEndsWithDot_returnsFalse() {
        assertFalse(validator.isValid("https://example.com/image.png.", context));
    }

    @Test
    void isValid_multipleExtensions_returnsTrue() {
        assertTrue(validator.isValid("https://example.com/image.backup.png", context));
    }

    @Test
    void isValid_customSupportedTypes_returnsTrue() {
        ValidateImageUrls customAnnotation = new ValidateImageUrls() {
            @Override
            public String message() {
                return "Invalid image URL";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public Class<? extends jakarta.validation.Payload>[] payload() {
                return new Class[0];
            }

            @Override
            public ImageType[] supportedTypes() {
                return new ImageType[]{ImageType.PNG, ImageType.JPG};
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return ValidateImageUrls.class;
            }
        };

        ImageUrlsValidator customValidator = new ImageUrlsValidator();
        customValidator.initialize(customAnnotation);

        assertTrue(customValidator.isValid("https://example.com/image.png", context));
        assertTrue(customValidator.isValid("https://example.com/image.jpg", context));
        assertFalse(customValidator.isValid("https://example.com/image.gif", context));
    }
}

