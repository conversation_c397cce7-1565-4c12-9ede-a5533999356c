package vinsf.miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import vinsf.miniapp.constant.ImageType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ImageLocaleMapValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class ImageLocaleMapValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    private ImageLocaleMapValidator validator;
    private ValidateImageLocaleMap annotation;

    @BeforeEach
    void setUp() {
        validator = new ImageLocaleMapValidator();
        
        // Create mock annotation with default supported types
        annotation = new ValidateImageLocaleMap() {
            @Override
            public String message() {
                return "Invalid image URL in locale map";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public Class<? extends jakarta.validation.Payload>[] payload() {
                return new Class[0];
            }

            @Override
            public ImageType[] supportedTypes() {
                return new ImageType[]{ImageType.PNG, ImageType.JPG, ImageType.JPEG, ImageType.GIF, ImageType.SVG, ImageType.WEBP};
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return ValidateImageLocaleMap.class;
            }
        };

        validator.initialize(annotation);

        // Setup mock behavior for constraint violation (lenient to avoid unnecessary stubbing failures)
        lenient().when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);
    }

    @Test
    void isValid_nullMap_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_emptyMap_returnsTrue() {
        Map<String, String> emptyMap = new HashMap<>();
        assertTrue(validator.isValid(emptyMap, context));
    }

    @Test
    void isValid_validImageUrls_returnsTrue() {
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "https://example.com/logo.png");
        validMap.put("vi", "https://example.com/logo.jpg");
        validMap.put("fr", "https://example.com/logo.webp");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_validImageUrlsWithQueryParams_returnsTrue() {
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "https://example.com/logo.png?size=large");
        validMap.put("vi", "https://cdn.example.com/images/logo.jpg?v=123");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_caseInsensitiveExtensions_returnsTrue() {
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "https://example.com/logo.PNG");
        validMap.put("vi", "https://example.com/logo.JpG");
        validMap.put("fr", "https://example.com/logo.WebP");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_invalidUrlFormat_returnsFalse() {
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "not-a-url");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Invalid URL format"));
    }

    @Test
    void isValid_invalidProtocol_returnsFalse() {
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "ftp://example.com/logo.png");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_unsupportedImageExtension_returnsFalse() {
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "https://example.com/document.pdf");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Invalid image extension"));
    }

    @Test
    void isValid_noFileExtension_returnsFalse() {
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "https://example.com/logo");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_mapWithNullValue_returnsTrue() {
        Map<String, String> mapWithNull = new HashMap<>();
        mapWithNull.put("en", "https://example.com/logo.png");
        mapWithNull.put("vi", null);

        assertTrue(validator.isValid(mapWithNull, context));
    }

    @Test
    void isValid_httpProtocol_returnsTrue() {
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "http://example.com/logo.png");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_svgExtension_returnsTrue() {
        Map<String, String> validMap = new HashMap<>();
        validMap.put("en", "https://example.com/icon.svg");

        assertTrue(validator.isValid(validMap, context));
    }

    @Test
    void isValid_multipleInvalidUrls_returnsFalseOnFirst() {
        Map<String, String> invalidMap = new HashMap<>();
        invalidMap.put("en", "invalid-url");
        invalidMap.put("vi", "also-invalid");

        assertFalse(validator.isValid(invalidMap, context));
        verify(context, atLeastOnce()).disableDefaultConstraintViolation();
    }
}

