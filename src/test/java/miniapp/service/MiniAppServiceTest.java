package miniapp.service;

import miniapp.constant.AppErrorCode;
import miniapp.constant.MiniAppStatus;
import miniapp.constant.MiniAppType;
import miniapp.dto.MiniAppDto;
import miniapp.dto.MiniAppFilter;
import miniapp.dto.ServiceResponse;
import miniapp.exception.BusinessLogicException;
import miniapp.mapper.MiniAppMapper;
import miniapp.model.MiniApp;
import miniapp.publisher.RedisPublisher;
import miniapp.repository.MiniAppRepository;
import miniapp.service.impl.MiniAppServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for MiniAppService
 *
 * <AUTHOR>
 * @since 2025-09-30
 */
@ExtendWith(MockitoExtension.class)
class MiniAppServiceTest {

    @Mock
    private MiniAppRepository miniAppRepository;

    @Mock
    private MiniAppMapper miniAppMapper;

    @Mock
    private RedisPublisher redisPublisher;

    @InjectMocks
    private MiniAppServiceImpl miniAppService;

    private MiniApp testEntity;
    private MiniAppDto testDto;

    @BeforeEach
    void setUp() {
        // Setup test data
        testEntity = new MiniApp();
        testEntity.setId("test-id-123");
        testEntity.setCode("test-code");
        testEntity.setName("Test MiniApp");
        testEntity.setType(MiniAppType.WEB);
        testEntity.setStatus(MiniAppStatus.ACTIVE);
        testEntity.setLogos(Map.of("en", "logo-url"));
        testEntity.setActive(true);

        testDto = new MiniAppDto(
                "test-id-123",
                "test-code",
                "Test MiniApp",
                null,
                null,
                null,
                MiniAppType.WEB,
                null,
                null,
                Map.of("en", "logo-url"),
                null,
                Set.of(),
                Set.of(),
                Set.of(),
                Set.of(),
                Set.of(),
                Set.of(),
                Map.of(),
                MiniAppStatus.ACTIVE,
                null, null, null, null, null, true
        );

        // Initialize the service caches
        miniAppService.init();
    }

    @Test
    void create_validDto_success() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(false);
        when(miniAppMapper.toEntity(any(MiniAppDto.class))).thenReturn(testEntity);
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When
        MiniAppDto result = miniAppService.create(testDto);

        // Then
        assertNotNull(result);
        assertEquals("test-code", result.code());
        assertEquals("Test MiniApp", result.name());
        verify(miniAppRepository).existsByCode("test-code");
        verify(miniAppRepository).save(any(MiniApp.class));
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void create_duplicateCode_throwsException() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(true);

        // When & Then
        BusinessLogicException exception = assertThrows(
                BusinessLogicException.class,
                () -> miniAppService.create(testDto)
        );

        ServiceResponse<?> response = exception.getPayload();
        assertEquals(AppErrorCode.RESOURCE_ALREADY_EXISTS.getCode(), response.getCode());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void getById_existingId_returnsDto() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When
        MiniAppDto result = miniAppService.getById("test-id-123");

        // Then
        assertNotNull(result);
        assertEquals("test-id-123", result.id());
        verify(miniAppRepository).findById("test-id-123");
    }

    @Test
    void getById_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        BusinessLogicException exception = assertThrows(
                BusinessLogicException.class,
                () -> miniAppService.getById("non-existing")
        );

        ServiceResponse<?> response = exception.getPayload();
        assertEquals(AppErrorCode.RESOURCE_NOT_FOUND.getCode(), response.getCode());
    }

    @Test
    void getByCode_existingCode_returnsDto() {
        // Given
        when(miniAppRepository.findByCode("test-code")).thenReturn(Optional.of(testEntity));
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When
        MiniAppDto result = miniAppService.getByCode("test-code");

        // Then
        assertNotNull(result);
        assertEquals("test-code", result.code());
        verify(miniAppRepository).findByCode("test-code");
    }

    @Test
    void getByCode_nonExistingCode_throwsException() {
        // Given
        when(miniAppRepository.findByCode("non-existing")).thenReturn(Optional.empty());

        // When & Then
        BusinessLogicException exception = assertThrows(
                BusinessLogicException.class,
                () -> miniAppService.getByCode("non-existing")
        );

        ServiceResponse<?> response = exception.getPayload();
        assertEquals(AppErrorCode.RESOURCE_NOT_FOUND.getCode(), response.getCode());
    }

    @Test
    void getById_cachedEntity_returnsDtoWithoutDatabaseCall() {
        // Given - First call to populate cache
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        miniAppService.getById("test-id-123");

        // Reset mock to verify no additional calls
        reset(miniAppRepository);
        lenient().when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When - Second call should use cache
        MiniAppDto result = miniAppService.getById("test-id-123");

        // Then
        assertNotNull(result);
        assertEquals("test-id-123", result.id());
        verify(miniAppRepository, never()).findById(anyString());
    }

    @Test
    void getByCode_cachedCode_returnsDtoWithoutDatabaseCall() {
        // Given - First call to populate cache
        when(miniAppRepository.findByCode("test-code")).thenReturn(Optional.of(testEntity));
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        miniAppService.getByCode("test-code");

        // Reset mock to verify no additional calls
        reset(miniAppRepository);
        lenient().when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When - Second call should use cache
        MiniAppDto result = miniAppService.getByCode("test-code");

        // Then
        assertNotNull(result);
        assertEquals("test-code", result.code());
        verify(miniAppRepository, never()).findByCode(anyString());
    }

    @Test
    void update_existingId_success() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        doNothing().when(miniAppMapper).update(any(MiniApp.class), any(MiniAppDto.class));

        // When
        MiniAppDto result = miniAppService.update("test-id-123", testDto);

        // Then
        assertNotNull(result);
        verify(miniAppMapper).update(eq(testEntity), eq(testDto));
        verify(miniAppRepository).save(testEntity);
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void update_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        BusinessLogicException exception = assertThrows(
                BusinessLogicException.class,
                () -> miniAppService.update("non-existing", testDto)
        );

        ServiceResponse<?> response = exception.getPayload();
        assertEquals(AppErrorCode.RESOURCE_NOT_FOUND.getCode(), response.getCode());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void updatePartial_existingId_success() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        doNothing().when(miniAppMapper).partialUpdate(any(MiniApp.class), any(MiniAppDto.class));

        // When
        MiniAppDto result = miniAppService.updatePartial("test-id-123", testDto);

        // Then
        assertNotNull(result);
        verify(miniAppMapper).partialUpdate(eq(testEntity), eq(testDto));
        verify(miniAppRepository).save(testEntity);
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void updatePartial_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        BusinessLogicException exception = assertThrows(
                BusinessLogicException.class,
                () -> miniAppService.updatePartial("non-existing", testDto)
        );

        ServiceResponse<?> response = exception.getPayload();
        assertEquals(AppErrorCode.RESOURCE_NOT_FOUND.getCode(), response.getCode());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void delete_existingId_setsActiveToFalse() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        miniAppService.delete("test-id-123");

        // Then
        ArgumentCaptor<MiniApp> entityCaptor = ArgumentCaptor.forClass(MiniApp.class);
        verify(miniAppRepository).save(entityCaptor.capture());
        MiniApp savedEntity = entityCaptor.getValue();
        assertFalse(savedEntity.getActive());
        verify(redisPublisher).sendChangeEvent(eq(MiniApp.ENTITY_NAME), eq("test-id-123"), eq("test-code"));
    }

    @Test
    void delete_nonExistingId_throwsException() {
        // Given
        when(miniAppRepository.findById("non-existing")).thenReturn(Optional.empty());

        // When & Then
        BusinessLogicException exception = assertThrows(
                BusinessLogicException.class,
                () -> miniAppService.delete("non-existing")
        );

        ServiceResponse<?> response = exception.getPayload();
        assertEquals(AppErrorCode.RESOURCE_NOT_FOUND.getCode(), response.getCode());
        verify(miniAppRepository, never()).save(any());
    }

    @Test
    void existsByCode_existingCode_returnsTrue() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(true);

        // When
        boolean result = miniAppService.existsByCode("test-code");

        // Then
        assertTrue(result);
        verify(miniAppRepository).existsByCode("test-code");
    }

    @Test
    void existsByCode_nonExistingCode_returnsFalse() {
        // Given
        when(miniAppRepository.existsByCode("non-existing")).thenReturn(false);

        // When
        boolean result = miniAppService.existsByCode("non-existing");

        // Then
        assertFalse(result);
        verify(miniAppRepository).existsByCode("non-existing");
    }

    @Test
    void search_withFilter_returnsPagedResults() {
        // Given
        MiniAppFilter filter = new MiniAppFilter(
                null,
                "test-code",
                "Test",
                1L,
                MiniAppType.WEB,
                MiniAppStatus.ACTIVE
        );
        Pageable pageable = PageRequest.of(0, 10);
        List<MiniApp> entities = List.of(testEntity);
        Page<MiniApp> entityPage = new PageImpl<>(entities, pageable, 1);

        when(miniAppRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(entityPage);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When
        Page<MiniAppDto> result = miniAppService.search(filter, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("test-code", result.getContent().get(0).code());
        verify(miniAppRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void search_withEmptyFilter_returnsAllResults() {
        // Given
        MiniAppFilter emptyFilter = new MiniAppFilter(null, null, null, null, null, null);
        Pageable pageable = PageRequest.of(0, 10);
        List<MiniApp> entities = List.of(testEntity);
        Page<MiniApp> entityPage = new PageImpl<>(entities, pageable, 1);

        when(miniAppRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(entityPage);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When
        Page<MiniAppDto> result = miniAppService.search(emptyFilter, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(miniAppRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void search_noResults_returnsEmptyPage() {
        // Given
        MiniAppFilter filter = new MiniAppFilter(null, "non-existing", null, null, null, null);
        Pageable pageable = PageRequest.of(0, 10);
        Page<MiniApp> emptyPage = new PageImpl<>(List.of(), pageable, 0);

        when(miniAppRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(emptyPage);

        // When
        Page<MiniAppDto> result = miniAppService.search(filter, pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
        verify(miniAppRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void invalidateCache_withIdsAndCodes_invalidatesBothCaches() {
        // Given
        Set<String> ids = Set.of("id1", "id2");
        Set<String> codes = Set.of("code1", "code2");

        // Populate cache first
        when(miniAppRepository.findById("id1")).thenReturn(Optional.of(testEntity));
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        miniAppService.getById("id1");

        // When
        miniAppService.invalidateCache(ids, codes);

        // Then - verify cache was invalidated by checking database is called again
        reset(miniAppRepository);
        when(miniAppRepository.findById("id1")).thenReturn(Optional.of(testEntity));
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        miniAppService.getById("id1");
        verify(miniAppRepository).findById("id1");
    }

    @Test
    void invalidateCache_emptyIdsAndCodes_doesNotThrowException() {
        // Given
        Set<String> emptyIds = Set.of();
        Set<String> emptyCodes = Set.of();

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> miniAppService.invalidateCache(emptyIds, emptyCodes));
    }

    @Test
    void create_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.existsByCode("test-code")).thenReturn(false);
        when(miniAppMapper.toEntity(any(MiniAppDto.class))).thenReturn(testEntity);
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);

        // When
        miniAppService.create(testDto);

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }

    @Test
    void update_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        doNothing().when(miniAppMapper).update(any(MiniApp.class), any(MiniAppDto.class));

        // When
        miniAppService.update("test-id-123", testDto);

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }

    @Test
    void updatePartial_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);
        when(miniAppMapper.toDto(any(MiniApp.class))).thenReturn(testDto);
        doNothing().when(miniAppMapper).partialUpdate(any(MiniApp.class), any(MiniAppDto.class));

        // When
        miniAppService.updatePartial("test-id-123", testDto);

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }

    @Test
    void delete_publishesEventAfterTransactionCommit() {
        // Given
        when(miniAppRepository.findById("test-id-123")).thenReturn(Optional.of(testEntity));
        when(miniAppRepository.save(any(MiniApp.class))).thenReturn(testEntity);

        // When
        miniAppService.delete("test-id-123");

        // Then - verify RedisPublisher is called with correct parameters
        verify(redisPublisher).sendChangeEvent(
                eq(MiniApp.ENTITY_NAME),
                eq("test-id-123"),
                eq("test-code")
        );
    }
}

