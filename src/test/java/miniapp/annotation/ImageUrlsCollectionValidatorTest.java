package miniapp.annotation;

import jakarta.validation.ConstraintValidatorContext;
import miniapp.constant.ImageType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ImageUrlsCollectionValidator
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@ExtendWith(MockitoExtension.class)
class ImageUrlsCollectionValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    private ImageUrlsCollectionValidator validator;
    private ValidateImageUrls annotation;

    @BeforeEach
    void setUp() {
        validator = new ImageUrlsCollectionValidator();
        
        // Create mock annotation with default supported types
        annotation = new ValidateImageUrls() {
            @Override
            public String message() {
                return "Invalid image URL";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public Class<? extends jakarta.validation.Payload>[] payload() {
                return new Class[0];
            }

            @Override
            public ImageType[] supportedTypes() {
                return new ImageType[]{ImageType.PNG, ImageType.JPG, ImageType.JPEG, ImageType.GIF, ImageType.SVG, ImageType.WEBP};
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return ValidateImageUrls.class;
            }
        };

        validator.initialize(annotation);

        // Setup mock behavior for constraint violation (lenient to avoid unnecessary stubbing failures)
        lenient().when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(violationBuilder);
    }

    @Test
    void isValid_nullCollection_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }

    @Test
    void isValid_emptyCollection_returnsTrue() {
        assertTrue(validator.isValid(new ArrayList<>(), context));
        assertTrue(validator.isValid(new HashSet<>(), context));
    }

    @Test
    void isValid_validImageUrlsList_returnsTrue() {
        List<String> validUrls = Arrays.asList(
                "https://example.com/image1.png",
                "https://example.com/image2.jpg",
                "https://example.com/image3.webp"
        );
        assertTrue(validator.isValid(validUrls, context));
    }

    @Test
    void isValid_validImageUrlsSet_returnsTrue() {
        Set<String> validUrls = new HashSet<>(Arrays.asList(
                "https://example.com/image1.png",
                "https://example.com/image2.jpg",
                "https://cdn.example.com/photo.gif"
        ));
        assertTrue(validator.isValid(validUrls, context));
    }

    @Test
    void isValid_collectionWithNullValue_returnsTrue() {
        List<String> urlsWithNull = new ArrayList<>();
        urlsWithNull.add("https://example.com/image.png");
        urlsWithNull.add(null);
        urlsWithNull.add("https://example.com/photo.jpg");

        assertTrue(validator.isValid(urlsWithNull, context));
    }

    @Test
    void isValid_invalidUrlInCollection_returnsFalse() {
        List<String> invalidUrls = Arrays.asList(
                "https://example.com/image.png",
                "not-a-url",
                "https://example.com/photo.jpg"
        );

        assertFalse(validator.isValid(invalidUrls, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Invalid image URL"));
    }

    @Test
    void isValid_emptyStringInCollection_returnsFalse() {
        List<String> invalidUrls = Arrays.asList(
                "https://example.com/image.png",
                "",
                "https://example.com/photo.jpg"
        );

        assertFalse(validator.isValid(invalidUrls, context));
        verify(context).disableDefaultConstraintViolation();
        verify(context).buildConstraintViolationWithTemplate(contains("Empty image URL"));
    }

    @Test
    void isValid_whitespaceOnlyInCollection_returnsFalse() {
        List<String> invalidUrls = Arrays.asList(
                "https://example.com/image.png",
                "   ",
                "https://example.com/photo.jpg"
        );

        assertFalse(validator.isValid(invalidUrls, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_unsupportedExtensionInCollection_returnsFalse() {
        List<String> invalidUrls = Arrays.asList(
                "https://example.com/image.png",
                "https://example.com/document.pdf",
                "https://example.com/photo.jpg"
        );

        assertFalse(validator.isValid(invalidUrls, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_invalidProtocolInCollection_returnsFalse() {
        List<String> invalidUrls = Arrays.asList(
                "https://example.com/image.png",
                "ftp://example.com/photo.jpg",
                "https://example.com/icon.svg"
        );

        assertFalse(validator.isValid(invalidUrls, context));
        verify(context).disableDefaultConstraintViolation();
    }

    @Test
    void isValid_whitespaceAroundValidUrls_returnsTrue() {
        List<String> validUrls = Arrays.asList(
                "  https://example.com/image.png  ",
                "  https://example.com/photo.jpg  ",
                "  https://cdn.example.com/icon.svg  "
        );
        assertTrue(validator.isValid(validUrls, context));
    }

    @Test
    void isValid_urlsWithQueryParameters_returnsTrue() {
        List<String> validUrls = Arrays.asList(
                "https://example.com/image.png?size=large",
                "https://cdn.example.com/photo.jpg?v=123",
                "https://example.com/icon.svg?quality=high"
        );
        assertTrue(validator.isValid(validUrls, context));
    }

    @Test
    void isValid_caseInsensitiveExtensions_returnsTrue() {
        List<String> validUrls = Arrays.asList(
                "https://example.com/image.PNG",
                "https://example.com/photo.JpG",
                "https://example.com/icon.WebP"
        );
        assertTrue(validator.isValid(validUrls, context));
    }

    @Test
    void isValid_mixedHttpAndHttps_returnsTrue() {
        List<String> validUrls = Arrays.asList(
                "http://example.com/image.png",
                "https://example.com/photo.jpg",
                "http://cdn.example.com/icon.svg"
        );
        assertTrue(validator.isValid(validUrls, context));
    }

    @Test
    void isValid_urlsWithPaths_returnsTrue() {
        List<String> validUrls = Arrays.asList(
                "https://example.com/images/gallery/photo1.jpg",
                "https://cdn.example.com/static/v1/icons/logo.png",
                "https://example.com/assets/images/banner.webp"
        );
        assertTrue(validator.isValid(validUrls, context));
    }

    @Test
    void isValid_noExtensionInCollection_returnsFalse() {
        List<String> invalidUrls = Arrays.asList(
                "https://example.com/image.png",
                "https://example.com/photo",
                "https://example.com/icon.svg"
        );

        assertFalse(validator.isValid(invalidUrls, context));
        verify(context).disableDefaultConstraintViolation();
    }
}

