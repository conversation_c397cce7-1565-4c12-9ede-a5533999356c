---
type: "agent_requested"
description: "Example description"
---

# Service Architecture & Patterns

## Service Layer Structure

### Entity Service (e.g., MiniAppService)
Entity services handle CRUD operations for a single entity/aggregate. They are responsible for:
- Data validation and business rules
- Caching (Caffeine local cache)
- Transaction management
- Publishing cache invalidation events

**Required Interface Methods:**
```java
// Create
EntityDto create(EntityDto dto);

// Read (with caching)
EntityDto getById(String id);  // throws exception if not found
EntityDto getByCode(String code);  // throws exception if not found
Optional<EntityDto> optById(String id);  // returns Optional
Optional<EntityDto> optByCode(String code);  // returns Optional

// Update
EntityDto update(String id, EntityDto dto);  // full update
EntityDto updatePartial(String id, EntityDto dto);  // partial update (null fields ignored)

// Delete (soft delete)
void delete(String id);

// Search
Page<EntityDto> search(EntityFilter filter, Pageable pageable);

// Utility
boolean existsByCode(String code);
void invalidateCache(Set<String> ids, Set<String> codes);
```

### Service Implementation Pattern
```java
@Slf4j
@Service
@RequiredArgsConstructor
@Validated
public class MiniAppServiceImpl implements MiniAppService {
    private static final int CACHE_TTL_MINUTES = 15;
    private static final int CACHE_MAX_SIZE = 100;

    private final MiniAppRepository repository;
    private final MiniAppMapper mapper;
    private final RedisPublisher redisPublisher;

    // Caffeine caches
    private Cache<String, MiniAppDto> idCache;
    private Cache<String, String> codeToIdCache;

    @PostConstruct
    public void init() {
        idCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        codeToIdCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_TTL_MINUTES, TimeUnit.MINUTES)
                .build();

        log.info("MiniAppService initialized with Caffeine cache");
    }

    // ... CRUD methods
}
```

## Caching Strategy

### Cache Structure
- **idCache**: `Cache<String, EntityDto>` - stores full DTO by ID
- **codeToIdCache**: `Cache<String, String>` - maps code to ID

### Cache Read Pattern
```java
@Override
public Optional<EntityDto> optById(String id) {
    log.debug("Getting Entity by id: {}", id);

    // Try cache first
    EntityDto cached = idCache.getIfPresent(id);
    if (cached != null) {
        log.debug("Entity found in cache for id: {}", id);
        return Optional.of(cached);
    }

    // Load from database
    Optional<EntityDto> entity = repository.findById(id).map(mapper::toDto);
    entity.ifPresent(this::putToCache);

    return entity;
}

@Override
public Optional<EntityDto> optByCode(String code) {
    log.debug("Getting Entity by code: {}", code);

    // Try to get id from code cache
    String cachedId = codeToIdCache.getIfPresent(code);
    if (cachedId != null) {
        log.debug("Found id in code cache for code: {}", code);
        return optById(cachedId);
    }

    // Load from database
    Optional<EntityDto> entity = repository.findByCode(code).map(mapper::toDto);
    entity.ifPresent(this::putToCache);

    return entity;
}

private void putToCache(EntityDto dto) {
    idCache.put(dto.id(), dto);
    codeToIdCache.put(dto.code(), dto.id());
}
```

### Cache Write Pattern
```java
@Override
@Transactional
public EntityDto create(@Valid EntityDto dto) {
    log.info("Creating Entity with code: {}", dto.code());

    // Validation
    if (repository.existsByCode(dto.code())) {
        log.warn("Entity code already exists: {}", dto.code());
        throw new BusinessLogicException(
            ServiceResponse.error(AppErrorCode.ENTITY_CODE_DUPLICATE)
        );
    }

    Entity entity = mapper.toEntity(dto);
    Entity saved = saveAndPublishChangeEvent(entity);

    log.info("Entity created successfully with id: {}", saved.getId());
    return mapper.toDto(saved);
}

private Entity saveAndPublishChangeEvent(Entity entity) {
    Entity saved = repository.save(entity);
    publishCacheInvalidation(saved.getId(), saved.getCode());
    return saved;
}
```

### Cache Invalidation Pattern
```java
private void publishCacheInvalidation(String id, String code) {
    if (TransactionSynchronizationManager.isActualTransactionActive()) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                redisPublisher.sendChangeEvent(Entity.ENTITY_NAME, id, code);
            }
        });
    } else {
        redisPublisher.sendChangeEvent(Entity.ENTITY_NAME, id, code);
    }
}

@Override
public void invalidateCache(Set<String> ids, Set<String> codes) {
    log.info("Invalidating cache for ids: {} and codes: {}", ids, codes);
    idCache.invalidateAll(ids);
    codeToIdCache.invalidateAll(codes);
}
```

## Redis Pub/Sub for Cache Invalidation

### Publisher Pattern
```java
@Component
@RequiredArgsConstructor
public class RedisPublisher {
    @Value("${redis.channel.change-event}")
    private String changeEventChannel;

    private final RedissonClient redissonClient;
    private final JsonUtil jsonUtil;
    private RTopic topic;

    @PostConstruct
    public void init() {
        topic = redissonClient.getTopic(changeEventChannel);
    }

    public void sendChangeEvent(String entityName, String id, String code) {
        var data = ChangeEvent.builder()
                .entity(entityName)
                .ids(Set.of(id))
                .codes(Set.of(code))
                .build();
        topic.publish(jsonUtil.toJson(data));
    }
}
```

### Listener Pattern
```java
@Component
@Slf4j
@RequiredArgsConstructor
public class RedisListener {
    @Value("${redis.channel.change-event}")
    private String changeEventChannel;

    private final RedissonClient redissonClient;
    private final JsonUtil jsonUtil;
    private final MiniAppService miniAppService;

    @PostConstruct
    public void init() {
        redissonClient.getTopic(changeEventChannel).addListener(String.class, (channel, msg) -> {
            log.info("Received change event message: {}", msg);
            var data = jsonUtil.fromJson(msg, ChangeEvent.class);
            switch (data.entity()) {
                case MiniApp.ENTITY_NAME -> miniAppService.invalidateCache(data.ids(), data.codes());
                default -> log.warn("Unknown entity type: {}", data.entity());
            }
        });
    }
}
```

### ChangeEvent DTO
```java
public record ChangeEvent(
    String entity,
    Set<String> ids,
    Set<String> codes
) {
}
```

## Update Patterns

### Full Update
```java
@Override
@Transactional
public EntityDto update(String id, @Valid EntityDto dto) {
    log.info("Updating Entity with id: {}", id);

    Entity existing = repository.findById(id)
            .orElseThrow(() -> {
                log.warn("Entity not found with id: {}", id);
                return new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.ENTITY_NOT_FOUND)
                );
            });

    // Update entity (all non-ignored fields)
    mapper.update(existing, dto);

    Entity saved = saveAndPublishChangeEvent(existing);

    log.info("Entity updated successfully with id: {}", id);
    return mapper.toDto(saved);
}
```

### Partial Update
```java
@Override
@Transactional
public EntityDto updatePartial(String id, EntityDto dto) {
    log.info("Partial updating Entity with id: {}", id);

    Entity existing = repository.findById(id)
            .orElseThrow(() -> {
                log.warn("Entity not found with id: {}", id);
                return new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.ENTITY_NOT_FOUND)
                );
            });

    // Apply partial updates (only non-null fields)
    mapper.partialUpdate(existing, dto);

    Entity saved = saveAndPublishChangeEvent(existing);

    log.info("Entity partially updated successfully with id: {}", id);
    return mapper.toDto(saved);
}
```

## Soft Delete Pattern
```java
@Override
@Transactional
public void delete(String id) {
    log.info("Deleting Entity with id: {}", id);

    Entity existing = repository.findById(id)
            .orElseThrow(() -> {
                log.warn("Entity not found with id: {}", id);
                return new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.ENTITY_NOT_FOUND)
                );
            });

    existing.setActive(false);
    saveAndPublishChangeEvent(existing);

    log.info("Entity deleted successfully with id: {}", id);
}
```

## Search with Specification Pattern
```java
@Override
@Transactional(readOnly = true)
public Page<EntityDto> search(EntityFilter filter, Pageable pageable) {
    log.debug("Searching Entities with filter: {}", filter);

    Specification<Entity> spec = EntitySpecification.fromFilter(filter);
    Page<Entity> page = repository.findAll(spec, pageable);

    return page.map(mapper::toDto);
}
```

## Specification Implementation
```java
public class EntitySpecification {

    private EntitySpecification() {
        // Utility class
    }

    public static Specification<Entity> fromFilter(EntityFilter filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter.id() != null) {
                predicates.add(criteriaBuilder.equal(root.get("id"), filter.id()));
            }

            if (filter.code() != null) {
                predicates.add(criteriaBuilder.equal(root.get("code"), filter.code()));
            }

            if (filter.name() != null) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")),
                    "%" + filter.name().toLowerCase() + "%"
                ));
            }

            if (filter.status() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), filter.status()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
```

## Logging Guidelines
- **log.info()**: Key lifecycle events (create, update, delete success)
- **log.debug()**: Detailed information for troubleshooting (cache hits, query details)
- **log.warn()**: Unusual but recoverable states (entity not found before throwing exception)
- **log.error()**: Execution failures (should be rare in service layer, mostly in exception handlers)
- Never log PII or large payloads
- Always log entity identifiers (id, code) for traceability

## Transaction Management
- Use `@Transactional` for write operations (create, update, delete)
- Use `@Transactional(readOnly = true)` for read operations (getById, search)
- Cache read methods (`optById`, `optByCode`) should NOT have `@Transactional` to avoid unnecessary transaction overhead
- Publish cache invalidation events AFTER transaction commit using `TransactionSynchronizationManager`

## Entity Relationship Guidelines
- Do **NOT** use entity relationship annotations (`@OneToMany`, `@ManyToOne`, `@OneToOne`, `@ManyToMany`)
- Model associations explicitly using ID fields (e.g., `categoryId`, `orgCode`)
- Use repositories or specifications to join/query across entities when needed
- Reason: improves clarity, avoids lazy-loading pitfalls, N+1 issues, and simplifies caching
