---
type: "always_apply"
---

# Project Conventions (Java 21 + Spring Boot 3.5)

## Naming & Style
- Variables: **camelCase**. Classes/Records/Enums: **PascalCase**. Packages: **lowercase**.
- Use `var` only when the type is obvious (e.g., `var data = ChangeEvent.builder()...`).
- Prefer `record` for DTOs, Filters, and immutable value objects.
- DTO suffix: `*Dto`. Filter: `*Filter`. Mapper: `*Mapper`.
- Repository: `*Repository`, extending `JpaRepository` and `JpaSpecificationExecutor`.
- Specification: `*Specification` (utility class with static methods).
- Services must always have **interfaces**: `XService` + `XServiceImpl`.
- Constants: Use `UPPER_SNAKE_CASE` for static final fields (e.g., `CACHE_TTL_MINUTES`, `ENTITY_NAME`).

## Layering
`Controller -> Business Service -> Entity Service -> Repository`
- **Controller**: input validation, mapping request/response, returns `ServiceResponse<T>`.
- **Business Service**: orchestrates multiple entity services, business rules.
- **Entity Service**: CRUD for a single aggregate/entity, caching, transaction management.
- **Repository**: persistence layer, extends `JpaRepository<Entity, ID>` and `JpaSpecificationExecutor<Entity>`.

## Annotations
- **Service classes**: `@Slf4j`, `@Service`, `@RequiredArgsConstructor`, `@Validated`
- **Controllers**: `@RestController`, `@RequestMapping`, `@RequiredArgsConstructor`
- **Repositories**: `@Repository` (interface only)
- **Mappers**: `@Mapper(componentModel = "spring")`
- **Entities**: `@Entity`, `@Table`, `@Data`, `@EqualsAndHashCode(callSuper = true)`, `@FieldNameConstants`
- **DTOs**: Use `@FieldNameConstants` on records for type-safe field references in mappers

## Exceptions
- Throw **BusinessLogicException** for business errors.
- Always wrap with `ServiceResponse.error(AppErrorCode)`.
- Example: `throw new BusinessLogicException(ServiceResponse.error(AppErrorCode.MINIAPP_NOT_FOUND));`
- Map all errors to **AppErrorCode** enum.
- Use `log.warn()` before throwing business exceptions.

## Documentation
- Javadoc for all public interfaces and complex logic.
- Class-level Javadoc: brief description + `<AUTHOR> + `@since yyyy-MM-dd`.
- Method-level Javadoc: describe purpose, parameters, and return values.
- Private helper methods: inline comments for complex logic only.

## Dependencies
- **Caching**: Caffeine for local cache, initialized in `@PostConstruct`.
- **Cache invalidation**: Redis Pub/Sub via `RedisPublisher` and `RedisListener`.
- **Validation**: `jakarta.validation` annotations on DTOs, `@Valid` on service method parameters.
- **Mapping**: MapStruct (preferred) with `@Mapper`, `@Mapping`, `@BeanMapping` annotations.
- **Transactions**: `@Transactional` for write operations, `@Transactional(readOnly = true)` for reads.
- **Logging**: Lombok `@Slf4j` for all service classes.
- **JSON**: Use `@JdbcTypeCode(SqlTypes.JSON)` with `columnDefinition = "jsonb"` for PostgreSQL JSON columns.

## Entity Patterns
- Extend `BaseEntityUUID` for UUID primary keys (uses UUIDv7).
- Extend `BaseEntityLong` for Long primary keys.
- `BaseEntity` provides: `version`, `createdBy`, `updatedBy`, `createdOn`, `updatedOn`, `tenantId`, `active`.
- Use `@FieldNameConstants` for type-safe field references.
- **No entity relationships**: Do NOT use `@OneToMany`, `@ManyToOne`, `@OneToOne`, `@ManyToMany`.
- Model associations with ID fields (e.g., `categoryId`, `orgCode`).
- Use `@Convert` for custom enum converters when needed.
- Soft delete: Set `active = false` instead of physical deletion.

## DTO Patterns
- Use Java `record` for immutability.
- Add validation annotations: `@NotNull`, `@NotBlank`, `@Size`, etc.
- Include audit fields: `version`, `createdBy`, `updatedBy`, `createdOn`, `updatedOn`, `tenantId`, `active`.
- Filter DTOs: all fields nullable, used for dynamic query building.

## Mapper Patterns
- Interface with `@Mapper(componentModel = "spring")`.
- Methods: `toDto(Entity)`, `toEntity(Dto)`, `update(@MappingTarget Entity, Dto)`, `partialUpdate(@MappingTarget Entity, Dto)`.
- Use `@Mapping(target = "field", ignore = true)` for id, code, audit fields in update methods.
- Use `@BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)` for partial updates.
- Use `MiniAppDto.Fields.fieldName` for type-safe field references.

## Repository Patterns
- Interface extending `JpaRepository<Entity, ID>` and `JpaSpecificationExecutor<Entity>`.
- Custom query methods: `Optional<Entity> findByCode(String code)`, `boolean existsByCode(String code)`.
- No `@Query` annotations unless absolutely necessary (prefer Specifications).

## Specification Patterns
- Utility class with private constructor.
- Static method: `public static Specification<Entity> fromFilter(EntityFilter filter)`.
- Build predicates dynamically, only add non-null filter values.
- Use `criteriaBuilder.and(predicates.toArray(new Predicate[0]))`.
- Support: `equal`, `like` (with lowercase), `in`, `between`.

## Tests
- Unit tests for Service/Specification using Mockito.
- Test class: `@ExtendWith(MockitoExtension.class)`.
- Mocks: `@Mock` for dependencies, `@InjectMocks` for service under test.
- Test method naming: `methodName_condition_expectedResult` (e.g., `create_duplicateCode_throwsException`).
- Verify exception payloads: `assertEquals(AppErrorCode.MINIAPP_CODE_DUPLICATE.getCode(), response.getCode())`.
- Use `@PostConstruct` method call in `@BeforeEach` for cache initialization.
