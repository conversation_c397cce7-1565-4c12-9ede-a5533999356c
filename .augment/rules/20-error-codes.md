---
type: "always_apply"
---

# Error Codes Rule (AppErrorCode)

## Format
- Code: `xxxxyyy` (7 digits).
  - `xxxx` = service code (1000–9999).
  - `yyy`  = error code increment (001+).
- Message: English, clear, no sensitive data.
- All business errors throw **BusinessLogicException** (not BusinessLoginException).
- Exception wraps `ServiceResponse.error(AppErrorCode)`.

## Error Code Allocation
```
// Common codes (0-999)
SUCCESS = 0
ERROR = -1
INTERNAL_SERVER_ERROR = 500
NOT_FOUND = 404

// Service-specific codes (xxxxyyy format)
// MiniApp Entity Service: 1000xxx
// MiniApp Management Service: 1001xxx
// MiniApp Bundle Service: 1002xxx
// MiniApp User Service: 1003xxx
```

## AppErrorCode Enum Structure
```java
@Getter
@RequiredArgsConstructor
public enum AppErrorCode {

    // Common codes
    SUCCESS(0, "Thành công"),
    ERROR(-1, "Có lỗi xảy ra"),

    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),
    NOT_FOUND(404, "%s Not Found"),

    // Service-specific codes
    // MiniApp Entity Service (1000xxx)
    MINIAPP_NOT_FOUND(1000001, "MiniApp not found"),
    MINIAPP_CODE_DUPLICATE(1000002, "MiniApp code already exists"),
    MINIAPP_INVALID_STATUS(1000003, "Invalid MiniApp status"),
    MINIAPP_INVALID_TYPE(1000004, "Invalid MiniApp type"),

    // Add more service codes here...
    ;

    private final int code;
    private final String message;
}
```

## Exception Handling Pattern

### Throwing Exceptions
```java
// Simple error
if (repository.existsByCode(dto.code())) {
    log.warn("MiniApp code already exists: {}", dto.code());
    throw new BusinessLogicException(
        ServiceResponse.error(AppErrorCode.MINIAPP_CODE_DUPLICATE)
    );
}

// Error with formatted message
throw new BusinessLogicException(
    ServiceResponse.error(AppErrorCode.NOT_FOUND, "MiniApp", "id", id)
);

// Error in Optional.orElseThrow()
return repository.findById(id)
    .orElseThrow(() -> {
        log.warn("MiniApp not found with id: {}", id);
        return new BusinessLogicException(
            ServiceResponse.error(AppErrorCode.MINIAPP_NOT_FOUND)
        );
    });
```

### BusinessLogicException
```java
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessLogicException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;
    private ServiceResponse<?> payload;

    public BusinessLogicException(ServiceResponse<?> payload) {
        this.payload = payload;
    }
}
```

### ServiceResponse Structure
```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceResponse<T> {
    private int code;

    @Builder.Default
    private List<String> messages = new ArrayList<>();

    private String message;
    private T data;

    // Success factory methods
    public static <T> ServiceResponse<T> success(T data) {
        ServiceResponse<T> res = ServiceResponse.<T>builder()
            .code(AppErrorCode.SUCCESS.getCode())
            .data(data)
            .build();
        res.message(AppErrorCode.SUCCESS.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> success(T data, String message) {
        ServiceResponse<T> res = ServiceResponse.<T>builder()
            .code(AppErrorCode.SUCCESS.getCode())
            .data(data)
            .build();
        res.message(message);
        return res;
    }

    // Error factory methods
    public static <T> ServiceResponse<T> error(AppErrorCode errorCodeEnum) {
        ServiceResponse<T> res = ServiceResponse.<T>builder()
            .code(errorCodeEnum.getCode())
            .build();
        res.message(errorCodeEnum.getMessage());
        return res;
    }

    public static <T> ServiceResponse<T> error(AppErrorCode errorCodeEnum, Object... args) {
        ServiceResponse<T> res = ServiceResponse.<T>builder()
            .code(errorCodeEnum.getCode())
            .build();
        res.message(String.format(errorCodeEnum.getMessage(), args));
        return res;
    }

    public static <T> ServiceResponse<T> error(T data, AppErrorCode errorCodeEnum) {
        ServiceResponse<T> res = ServiceResponse.<T>builder()
            .code(errorCodeEnum.getCode())
            .data(data)
            .build();
        res.message(errorCodeEnum.getMessage());
        return res;
    }
}
```

## Global Exception Handler
```java
@ControllerAdvice
public class RestControllerExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler({BusinessLogicException.class})
    public ResponseEntity<ServiceResponse<?>> handleBusinessLogicException(BusinessLogicException ex) {
        ServiceResponse<?> response = ex.getPayload();
        if (response != null) {
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        } else {
            return handleAllExceptions(ex);
        }
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ServiceResponse<?>> handleAllExceptions(Exception ex) {
        ServiceResponse<Object> response = ServiceResponse.builder()
                .data(null)
                .code(AppErrorCode.INTERNAL_SERVER_ERROR.getCode())
                .message(ex.getMessage() != null ? ex.getMessage() : "Internal Server Error")
                .build();
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
}
```

## Testing Exception Handling
```java
@Test
void create_duplicateCode_throwsException() {
    // Given
    when(repository.existsByCode("test-code")).thenReturn(true);

    // When & Then
    BusinessLogicException exception = assertThrows(
        BusinessLogicException.class,
        () -> service.create(testDto)
    );

    ServiceResponse<?> response = exception.getPayload();
    assertEquals(AppErrorCode.MINIAPP_CODE_DUPLICATE.getCode(), response.getCode());
    assertEquals(AppErrorCode.MINIAPP_CODE_DUPLICATE.getMessage(), response.getMessage());
    verify(repository, never()).save(any());
}

@Test
void getById_nonExistingId_throwsException() {
    // Given
    when(repository.findById("non-existing")).thenReturn(Optional.empty());

    // When & Then
    BusinessLogicException exception = assertThrows(
        BusinessLogicException.class,
        () -> service.getById("non-existing")
    );

    ServiceResponse<?> response = exception.getPayload();
    assertEquals(AppErrorCode.MINIAPP_NOT_FOUND.getCode(), response.getCode());
}
```

## Best Practices
1. **Always log before throwing**: Use `log.warn()` to log context before throwing business exceptions
2. **Use specific error codes**: Create specific error codes for each business error scenario
3. **Consistent error messages**: Keep error messages clear, concise, and in English
4. **No sensitive data**: Never include passwords, tokens, or PII in error messages
5. **Format support**: Use `String.format()` for dynamic error messages with placeholders
6. **Test exception paths**: Always write tests for exception scenarios to verify error codes and messages